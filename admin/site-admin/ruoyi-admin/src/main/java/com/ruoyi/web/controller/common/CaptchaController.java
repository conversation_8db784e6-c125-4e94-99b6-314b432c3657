package com.ruoyi.web.controller.common;

import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.concurrent.TimeUnit;
import javax.imageio.ImageIO;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginBody;
import com.ruoyi.common.utils.RsaUtil;
import com.ruoyi.common.utils.sms.SmsUtils;
import com.ruoyi.system.service.ISysUserService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.FastByteArrayOutputStream;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.google.code.kaptcha.Producer;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.sign.Base64;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.system.service.ISysConfigService;

/**
 * 验证码操作处理
 * 
 * <AUTHOR>
 */
@RestController
public class CaptchaController
{
    @Resource(name = "captchaProducer")
    private Producer captchaProducer;

    @Resource(name = "captchaProducerMath")
    private Producer captchaProducerMath;

    @Resource
    private RedisCache redisCache;

    @Resource
    private ISysConfigService configService;

    @Resource
    private ISysUserService userService;

    @Resource
    private SmsUtils smsUtils;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private Environment environment;

    /**
     * 生成验证码
     */
    @GetMapping("/captchaImage")
    public AjaxResult getCode(HttpServletResponse response) throws IOException
    {
        AjaxResult ajax = AjaxResult.success();
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        ajax.put("captchaEnabled", captchaEnabled);
        if (!captchaEnabled)
        {
            return ajax;
        }

        // 保存验证码信息
        String uuid = IdUtils.simpleUUID();
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;

        String capStr = null, code = null;
        BufferedImage image = null;

        // 生成验证码
        String captchaType = RuoYiConfig.getCaptchaType();
        if ("math".equals(captchaType))
        {
            String capText = captchaProducerMath.createText();
            capStr = capText.substring(0, capText.lastIndexOf("@"));
            code = capText.substring(capText.lastIndexOf("@") + 1);
            image = captchaProducerMath.createImage(capStr);
        }
        else if ("char".equals(captchaType))
        {
            capStr = code = captchaProducer.createText();
            image = captchaProducer.createImage(capStr);
        }

        redisCache.setCacheObject(verifyKey, code, Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);
        // 转换流信息写出
        FastByteArrayOutputStream os = new FastByteArrayOutputStream();
        try
        {
            ImageIO.write(image, "jpg", os);
        }
        catch (IOException e)
        {
            return AjaxResult.error(e.getMessage());
        }

        ajax.put("uuid", uuid);
        ajax.put("img", Base64.encode(os.toByteArray()));
        return ajax;
    }

    @PostMapping("getVerificationCode")
    public AjaxResult getVerificationCode(@RequestBody String data) throws Exception {
        LoginBody loginBody = JSONObject.parseObject(RsaUtil.decryptByPrivateKey(data, RsaUtil.defaultPrivateKey), LoginBody.class);
        SysUser user = userService.selectUserByUserName(loginBody.getUsername());
        if (user == null) {
            throw new RuntimeException("无对应账号，请重新输入！");
        }
        if (user.getPhonenumber()==null || user.getPhonenumber().trim().length()!=11) {
            throw new RuntimeException("该账号未绑定手机号，请联系管理员绑定！");
        }

        String phoneNumber = user.getPhonenumber();

        // 检查用户在1分钟内是否已经获取过验证码
        // 构造Redis键
        String key = user.getUserName() + ":" + phoneNumber + ":login:lastSendTime";

        // 从Redis获取上次发送验证码的时间
        String lastSendTimeString = stringRedisTemplate.opsForValue().get(key);
        Long lastSendTime = null;
        if (lastSendTimeString != null && !lastSendTimeString.isEmpty()) {
            try {
                lastSendTime = Long.parseLong(lastSendTimeString);
            } catch (NumberFormatException e) {
                // 处理转换异常
                e.printStackTrace();
            }
        }

        // 检查是否在一分钟内发送过验证码
        if (lastSendTime != null && (System.currentTimeMillis() - lastSendTime) < 60000) {
            // 如果距离上次发送验证码不到1分钟，提示用户等待
            return AjaxResult.error("您在一分钟内只能获取一次验证码，请等待1分钟后再次尝试。");
        }

        // 生成验证码
        String code = SmsUtils.generateRandomCode(6);

        // 发送短信
        boolean result = false;
        try {
            result = smsUtils.send(phoneNumber, "你的短信验证码："+code+"，该验证码5分钟内有效，请尽快输入。");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if (!result) {
            // 如果在测试环境和开发环境短信发送失败，默认code为123456
            String activeProfiles = environment.getActiveProfiles().toString();
            if (activeProfiles.contains("dev") || activeProfiles.contains("test")) {
                redisCache.setCacheObject(user.getUserName() + ":" + phoneNumber+":loginht", "123456", 5, TimeUnit.MINUTES);
                return AjaxResult.error("短信发送失败，可以直接输：123456");
            }
            return AjaxResult.error("短信发送失败");
        }

        // 将验证码存入Redis，有效期5分钟
        redisCache.setCacheObject(user.getUserName() + ":" + phoneNumber+":loginht", code, 5, TimeUnit.MINUTES);

        // 更新最后一次发送验证码的时间，有效期1分钟
        redisCache.setCacheObject(key, System.currentTimeMillis(), 1, TimeUnit.MINUTES);

        return AjaxResult.success("", code);

    }


}
