<script setup lang="ts">
import {VueDraggable} from 'vue-draggable-plus'
import {computed, ref} from 'vue'

// TypeScript 接口定义
interface ModuleTemplate {
  id: number
  title: string
  subtitle: string
  icon: string
  color: string
  width: number
  height: number
}

interface CanvasModule extends ModuleTemplate {
  templateId: number
  sort: number
  hidden: boolean
}

interface IconComponents {
  [key: string]: string
}

interface DragOptions {
  animation: number
  ghostClass: string
  chosenClass: string
  dragClass: string
  handle: string
}

const iconComponents: IconComponents = {
  "Bell": '<svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 19V20H3V19L5 17V11C5 7.9 7 5.2 10 4.3V4C10 2.3 11.3 1 13 1S16 2.3 16 4V4.3C19 5.2 21 7.9 21 11V17L23 19Z"/></svg>',
  "Mail": '<svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24"><path d="M20 4H4C2.9 4 2.01 4.9 2.01 6L2 18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6C22 4.9 21.1 4 20 4ZM20 8L12 13L4 8V6L12 11L20 6V8Z"/></svg>',
  "FileText": '<svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24"><path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/></svg>',
  "Navigation": '<svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24"><path d="M12,2A7,7 0 0,1 19,9C19,14.25 12,22 12,22C12,22 5,14.25 5,9A7,7 0 0,1 12,2M12,4A5,5 0 0,0 7,9C7,13 12,18.71 12,18.71C12,18.71 17,13 17,9A5,5 0 0,0 12,4M12,7A2,2 0 0,1 14,9A2,2 0 0,1 12,11A2,2 0 0,1 10,9A2,2 0 0,1 12,7Z"/></svg>',
  "Edit": '<svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24"><path d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"/></svg>',
  "Camera": '<svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24"><path d="M4,4H7L9,2H15L17,4H20A2,2 0 0,1 22,6V18A2,2 0 0,1 20,20H4A2,2 0 0,1 2,18V6A2,2 0 0,1 4,4M12,7A5,5 0 0,0 7,12A5,5 0 0,0 12,17A5,5 0 0,0 17,12A5,5 0 0,0 12,7M12,9A3,3 0 0,1 15,12A3,3 0 0,1 12,15A3,3 0 0,1 9,12A3,3 0 0,1 12,9Z"/></svg>',
  "MapPin": '<svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24"><path d="M12,11.5A2.5,2.5 0 0,1 9.5,9A2.5,2.5 0 0,1 12,6.5A2.5,2.5 0 0,1 14.5,9A2.5,2.5 0 0,1 12,11.5M12,2A7,7 0 0,0 5,9C5,14.25 12,22 12,22C12,22 19,14.25 19,9A7,7 0 0,0 12,2Z"/></svg>',
  "Download": '<svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24"><path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z"/></svg>',
  "Phone": '<svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24"><path d="M6.62,10.79C8.06,13.62 10.38,15.94 13.21,17.38L15.41,15.18C15.69,14.9 16.08,14.82 16.43,14.93C17.55,15.3 18.75,15.5 20,15.5A1,1 0 0,1 21,16.5V20A1,1 0 0,1 20,21A17,17 0 0,1 3,4A1,1 0 0,1 4,3H7.5A1,1 0 0,1 8.5,4C8.5,5.25 8.7,6.45 9.07,7.57C9.18,7.92 9.1,8.31 8.82,8.59L6.62,10.79Z"/></svg>',
  "Users": '<svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24"><path d="M12,5.5A3.5,3.5 0 0,1 15.5,9A3.5,3.5 0 0,1 12,12.5A3.5,3.5 0 0,1 8.5,9A3.5,3.5 0 0,1 12,5.5M5,8C5.56,8 6.08,8.15 6.53,8.42C6.38,9.85 6.8,11.27 7.66,12.38C7.16,13.34 6.16,14 5,14A3,3 0 0,1 2,11A3,3 0 0,1 5,8M19,8A3,3 0 0,1 22,11A3,3 0 0,1 19,14C17.84,14 16.84,13.34 16.34,12.38C17.2,11.27 17.62,9.85 17.47,8.42C17.92,8.15 18.44,8 19,8M5.5,18.25C5.5,16.18 8.41,14.5 12,14.5C15.59,14.5 18.5,16.18 18.5,18.25V20H5.5V18.25Z"/></svg>'
};

// 显示配置面板
const selectedModule = ref<CanvasModule | null>(null);
const showModuleDialog = ref<boolean>(false);
const showPreviewDialog = ref<boolean>(false);

// 所有可用的模块模板
const availableModules = ref<ModuleTemplate[]>([
  {
    id: 1,
    title: '会议通知',
    subtitle: 'Notification',
    icon: 'Bell',
    color: 'from-purple-400 to-purple-600',
    width: 50,
    height: 100
  },
  {
    id: 2,
    title: '参会报名',
    subtitle: 'Sign Up',
    icon: 'Mail',
    color: 'from-orange-400 to-orange-600',
    width: 50,
    height: 100
  },
  {
    id: 3,
    title: '会议议程',
    subtitle: 'Agenda',
    icon: 'FileText',
    color: 'from-pink-400 to-pink-600',
    width: 50,
    height: 200
  },
  {
    id: 4,
    title: '地图导航',
    subtitle: 'Navigation',
    icon: 'Navigation',
    color: 'from-cyan-400 to-cyan-600',
    width: 50,
    height: 200
  },
  {
    id: 5,
    title: '会议签到',
    subtitle: 'Sign In',
    icon: 'Edit',
    color: 'from-green-400 to-green-600',
    width: 50,
    height: 100
  },
  {
    id: 6,
    title: '图文直播',
    subtitle: 'Live Streaming',
    icon: 'Camera',
    color: 'from-pink-400 to-red-500',
    width: 50,
    height: 200
  },
  {
    id: 7,
    title: '座位图',
    subtitle: 'Seat Map',
    icon: 'MapPin',
    color: 'from-purple-500 to-indigo-600',
    width: 50,
    height: 100
  },
  {
    id: 8,
    title: '资料下载',
    subtitle: 'Download',
    icon: 'Download',
    color: 'from-red-400 to-red-700',
    width: 50,
    height: 100
  },
  {
    id: 9,
    title: '联系会务组',
    subtitle: 'Contact Us',
    icon: 'Phone',
    color: 'from-purple-500 to-purple-700',
    width: 50,
    height: 100
  },
  {
    id: 10,
    title: '我的信息',
    subtitle: 'My Information',
    icon: 'Users',
    color: 'from-green-400 to-green-600',
    width: 50,
    height: 100
  }
]);

// 当前设计画布中的模块
const canvasModules = ref<CanvasModule[]>([]);

// 计算可用的模块（未被添加到画布的模块）
const availableModulesForLibrary = computed<ModuleTemplate[]>(() => {
  const usedModuleIds: number[] = canvasModules.value.map(m => m.templateId);
  return availableModules.value.filter(module => !usedModuleIds.includes(module.id));
});

// 生成唯一ID
let nextId: number = 1000;
const generateId = (): number => ++nextId;

// 添加模块到画布
const addModuleToCanvas = (moduleTemplate: ModuleTemplate): void => {
  const newModule: CanvasModule = {
    id: generateId(),
    templateId: moduleTemplate.id,
    title: moduleTemplate.title,
    subtitle: moduleTemplate.subtitle,
    icon: moduleTemplate.icon,
    color: moduleTemplate.color,
    width: moduleTemplate.width,
    height: moduleTemplate.height,
    sort: canvasModules.value.length + 1,
    hidden: false
  };
  canvasModules.value.push(newModule);
};

// 从画布删除模块
const removeModuleFromCanvas = (moduleId: number): void => {
  const index: number = canvasModules.value.findIndex(m => m.id === moduleId);
  if (index !== -1) {
    canvasModules.value.splice(index, 1);
  }
};

// 工具函数
const getColorClass = (color: string): string => {
  return 'bg-gradient-to-br ' + color;
};

const getWidthClass = (width: number): string => {
  return width === 100 ? 'col-span-2' : 'col-span-1';
};

const getHeightClass = (height: number): string => {
  return height === 200 ? 'row-span-2' : 'row-span-1';
};

const getHeightPx = (height: number): string => {
  return height === 200 ? 'h-[208px]' : 'h-[100px]';
};

// 更新模块配置
const updateModule = (id: number, updates: Partial<CanvasModule>): void => {
  const index: number = canvasModules.value.findIndex(m => m.id === id);
  if (index !== -1) {
    canvasModules.value[index] = { ...canvasModules.value[index], ...updates };
  }
};

// 获取图标HTML
const getIconHtml = (iconName: string): string => {
  return iconComponents[iconName] || '';
};

// 拖拽配置 - 画布内模块排序
const canvasDragOptions: DragOptions = {
  animation: 150,
  ghostClass: 'sortable-ghost',
  chosenClass: 'sortable-chosen',
  dragClass: 'sortable-drag',
  handle: '.drag-handle'
};

// 预览功能
const openPreview = (): void => {
  showPreviewDialog.value = true;
};

// 获取预览模块的样式类
const getPreviewModuleClass = (module: CanvasModule): string => {
  return module.width === 100 ? 'col-span-2' : 'col-span-1';
};

// 获取预览模块的内联样式
const getPreviewModuleStyle = (module: CanvasModule): { height: string } => {
  return {
    height: `${module.height}px`
  };
};
</script>

<template>
  <div class="bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700">
    <div class="min-h-screen p-4">
      <!-- 标题栏 -->
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-white">会议模块设计器</h1>
        <div class="flex gap-2">
          <el-button type="success" icon="Check">
            保存配置
          </el-button>
          <el-button type="primary" icon="View" @click="openPreview">
            预览
          </el-button>
        </div>
      </div>

      <div class="flex gap-4 w-[960px] mx-auto">
        <!-- 模块库面板 -->
        <div class="flex-1 bg-white/10 backdrop-blur-sm rounded-xl p-4">
          <h2 class="text-lg font-bold text-white mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
            </svg>
            模块库
          </h2>
          
          <div class="space-y-2 max-h-[calc(100vh-200px)] overflow-y-auto">
            <div
                v-for="module in availableModulesForLibrary"
                :key="module.id"
                :class="`${getColorClass(module.color)} cursor-pointer`"
                class="rounded-lg p-3 text-white transform transition-all duration-200 hover:shadow-lg"
                @click="addModuleToCanvas(module)"
            >
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="font-semibold text-sm">{{ module.title }}</h3>
                  <p class="text-white/80 text-xs">{{ module.subtitle }}</p>
                </div>
                <div class="bg-white/20 p-2 rounded-full" v-html="getIconHtml(module.icon)"></div>
              </div>
            </div>
          </div>
          
          <div v-if="availableModulesForLibrary.length === 0" class="text-white/60 text-center py-8">
            <svg class="w-12 h-12 mx-auto mb-2 opacity-50" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
              <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
            </svg>
            <p class="text-sm">所有模块已添加</p>
          </div>
        </div>

        <!-- 设计画布 -->
        <div class="w-[375px] bg-white/5 backdrop-blur-sm rounded-xl p-4">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-bold text-white flex items-center">
              <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011-1z" clip-rule="evenodd"/>
              </svg>
              设计画布
            </h2>
            <div class="text-white/60 text-sm">
              {{ canvasModules.length }} 个模块
            </div>
          </div>

          <div
              v-if="canvasModules.length === 0"
              class="border-2 border-dashed border-white/30 rounded-xl p-16 text-center bg-white/5 backdrop-blur-sm min-h-[400px] flex flex-col justify-center"
          >
            <div class="mb-6">
              <svg class="w-20 h-20 mx-auto mb-4 text-white/40" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
              </svg>
              <p class="text-white/70 text-xl mb-3 font-medium">拖拽放置区域</p>
              <p class="text-white/50 text-base mb-4">从左侧模块库点击模块添加到此处</p>
            </div>

            <div class="flex justify-center items-center space-x-4 text-white/40">
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                  <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                </svg>
                <span class="text-sm">点击添加</span>
              </div>
              <div class="w-px h-4 bg-white/20"></div>
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                </svg>
                <span class="text-sm">拖拽排序</span>
              </div>
              <div class="w-px h-4 bg-white/20"></div>
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"/>
                </svg>
                <span class="text-sm">自定义配置</span>
              </div>
            </div>
          </div>

          <VueDraggable
              v-else
              v-model="canvasModules"
              :options="canvasDragOptions"
              class="grid grid-cols-2 gap-4 grid-auto-rows min-h-[100px]"
          >
            <div
                v-for="module in canvasModules"
                :key="module.id"
                :class="`${getWidthClass(module.width)} ${getHeightClass(module.height)} ${getHeightPx(module.height)} ${getColorClass(module.color)}`"
                class="rounded-2xl p-6 text-white transform transition-all duration-300 shadow-xl hover:shadow-2xl hover:scale-102 flex flex-col justify-between group relative drag-handle"
                @click="selectedModule = module; showModuleDialog = true"
            >
              <!-- 删除按钮 -->
              <el-button
                  @click.stop="removeModuleFromCanvas(module.id)"
                  class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                  type="danger"
                  size="small"
                  :icon="'Close'"
                  circle
              />

              <!-- 拖拽指示器 -->
              <div class="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <div class="bg-black/30 text-white p-1 rounded">
                  <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
                    <circle cx="2" cy="2" r="1"/>
                    <circle cx="6" cy="2" r="1"/>
                    <circle cx="10" cy="2" r="1"/>
                    <circle cx="2" cy="6" r="1"/>
                    <circle cx="6" cy="6" r="1"/>
                    <circle cx="10" cy="6" r="1"/>
                    <circle cx="2" cy="10" r="1"/>
                    <circle cx="6" cy="10" r="1"/>
                    <circle cx="10" cy="10" r="1"/>
                  </svg>
                </div>
              </div>

              <!-- 模块内容 -->
              <div class="flex flex-col h-full justify-between pointer-events-none">
                <div>
                  <h3 class="text-xl font-bold mb-2">{{ module.title }}</h3>
                  <p class="text-white/80 text-sm">{{ module.subtitle }}</p>
                </div>

                <div class="flex justify-end">
                  <div class="bg-white/20 p-3 rounded-full group-hover:bg-white/30 transition-colors"
                       v-html="getIconHtml(module.icon)">
                  </div>
                </div>
              </div>

              <!-- 配置信息显示 -->
              <div class="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <div class="bg-black/50 text-white text-xs px-2 py-1 rounded">
                  {{ module.width }}% × {{ module.height }}px
                </div>
              </div>
            </div>
          </VueDraggable>
        </div>

        <!-- 配置面板 -->
        <div class="flex-1 bg-white/10 backdrop-blur-sm rounded-xl p-4 max-h-[calc(100vh-120px)] overflow-y-auto">
          <h2 class="text-lg font-bold text-white mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"/>
            </svg>
            配置面板
          </h2>

          <div v-if="canvasModules.length === 0" class="text-white/60 text-center py-8">
            <svg class="w-12 h-12 mx-auto mb-2 opacity-50" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"/>
            </svg>
            <p class="text-sm">暂无模块可配置</p>
          </div>

          <div v-for="module in canvasModules" :key="module.id" class="bg-white/20 backdrop-blur-sm p-4 rounded-lg mb-4">
            <h3 class="font-semibold mb-3 text-white">{{ module.title }} 配置</h3>

            <div class="space-y-3">
              <div>
                <label class="block text-sm font-medium mb-1 text-white/80">宽度</label>
                <el-select
                    :model-value="module.width"
                    @update:model-value="updateModule(module.id, { width: parseInt($event) })"
                    class="w-full"
                    placeholder="选择宽度"
                >
                  <el-option label="50% (半宽)" :value="50" />
                  <el-option label="100% (全宽)" :value="100" />
                </el-select>
              </div>

              <div>
                <label class="block text-sm font-medium mb-1 text-white/80">高度</label>
                <el-select
                    :model-value="module.height"
                    @update:model-value="updateModule(module.id, { height: parseInt($event) })"
                    class="w-full"
                    placeholder="选择高度"
                >
                  <el-option label="100px (标准高度)" :value="100" />
                  <el-option label="200px (双倍高度)" :value="200" />
                </el-select>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 模块详情弹窗 -->
      <el-dialog
          v-model="showModuleDialog"
          :title="selectedModule?.title || '模块详情'"
          width="500px"
          :before-close="() => { selectedModule = null; showModuleDialog = false; }"
      >
        <div v-if="selectedModule">
          <div class="flex justify-between items-start mb-6">
            <div>
              <h3 class="text-xl font-bold mb-2">{{ selectedModule.title }}</h3>
              <p class="text-gray-600">{{ selectedModule.subtitle }}</p>
            </div>
            <div :class="`bg-gradient-to-br ${selectedModule.color} p-3 rounded-full text-white`" v-html="getIconHtml(selectedModule.icon)"></div>
          </div>

          <div class="space-y-4 text-sm text-gray-600 mb-6">
            <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <span class="font-medium">宽度:</span>
              <span class="text-blue-600 font-semibold">{{ selectedModule.width }}% ({{ selectedModule.width === 100 ? '全宽' : '半宽' }})</span>
            </div>
            <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <span class="font-medium">高度:</span>
              <span class="text-blue-600 font-semibold">{{ selectedModule.height }}px ({{ selectedModule.height === 400 ? '双倍' : '标准' }}高度)</span>
            </div>
            <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <span class="font-medium">网格跨度:</span>
              <span class="text-blue-600 font-semibold">{{ selectedModule.width === 100 ? '2列' : '1列' }} × {{ selectedModule.height === 400 ? '2行' : '1行' }}</span>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="flex gap-2">
            <el-button @click="selectedModule = null; showModuleDialog = false">
              关闭
            </el-button>
            <el-button
                v-if="selectedModule && selectedModule.id > 1000"
                type="danger"
                @click="removeModuleFromCanvas(selectedModule.id); selectedModule = null; showModuleDialog = false"
            >
              删除模块
            </el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 移动端预览弹窗 -->
      <el-dialog
          v-model="showPreviewDialog"
          title="移动端预览"
          width="420px"
          :before-close="() => { showPreviewDialog = false; }"
          class="preview-dialog"
      >
        <div class="mobile-preview-container">
          <!-- 移动端框架 -->
          <div class="mobile-frame">
            <!-- 状态栏 -->
            <div class="status-bar">
              <div class="status-left">
                <span class="time">9:41</span>
              </div>
              <div class="status-right">
                <div class="signal-icons">
                  <div class="signal"></div>
                  <div class="wifi"></div>
                  <div class="battery"></div>
                </div>
              </div>
            </div>

            <!-- 应用标题栏 -->
            <div class="app-header">
              <h2 class="app-title">会议模块</h2>
            </div>

            <!-- 模块内容区域 -->
            <div class="modules-container">
              <div v-if="canvasModules.length === 0" class="empty-preview">
                <svg class="w-16 h-16 mx-auto mb-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                </svg>
                <p class="text-gray-500 text-sm">暂无模块内容</p>
              </div>

              <div v-else class="grid grid-cols-2 gap-3 p-4 auto-rows-min">
                <div
                    v-for="module in canvasModules"
                    :key="module.id"
                    :class="`${getPreviewModuleClass(module)} ${getColorClass(module.color)}`"
                    :style="getPreviewModuleStyle(module)"
                    class="rounded-xl p-3 text-white flex flex-col justify-between shadow-lg"
                >
                  <div>
                    <h3 class="font-semibold text-sm mb-1">{{ module.title }}</h3>
                    <p class="text-white/80 text-xs">{{ module.subtitle }}</p>
                  </div>
                  <div class="flex justify-end">
                    <div class="bg-white/20 p-1.5 rounded-full preview-icon" v-html="getIconHtml(module.icon)"></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 底部导航栏 -->
            <div class="bottom-nav">
              <div class="nav-indicator"></div>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-center">
            <el-button @click="showPreviewDialog = false">
              关闭预览
            </el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 使用说明 -->
      <div class="mt-6 bg-white/10 backdrop-blur-sm rounded-xl p-4">
        <h3 class="text-white font-semibold mb-3 flex items-center">
          <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
          </svg>
          使用说明
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-white/70 text-sm">
          <div>
            <h4 class="font-medium text-white mb-2">模块库</h4>
            <ul class="space-y-1">
              <li>• 点击模块添加到画布</li>
              <li>• 每个模块只能添加一次</li>
              <li>• 已添加的模块会从库中消失</li>
            </ul>
          </div>
          <div>
            <h4 class="font-medium text-white mb-2">设计画布</h4>
            <ul class="space-y-1">
              <li>• 拖拽模块调整顺序</li>
              <li>• 点击删除按钮移除模块</li>
              <li>• 点击模块查看详情</li>
            </ul>
          </div>
          <div>
            <h4 class="font-medium text-white mb-2">配置面板</h4>
            <ul class="space-y-1">
              <li>• 调整模块宽度和高度</li>
              <li>• 支持50%/100%宽度</li>
              <li>• 支持200px/400px高度</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>

</template>

<style scoped>
.sortable-ghost {
  opacity: 0.4;
}
.sortable-chosen {
  transform: scale(1.02);
}
.sortable-drag {
  transform: rotate(5deg);
  opacity: 0.8;
}
.grid-auto-rows {
  grid-auto-rows: 200px;
}
.drag-handle {
  cursor: grab;
}
.drag-handle:active {
  cursor: grabbing;
}

/* 移动端预览样式 */
.preview-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.mobile-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.mobile-frame {
  width: 375px;
  height: 667px;
  background: #000;
  border-radius: 25px;
  padding: 8px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
}

.mobile-frame::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  border-radius: 17px;
  z-index: 1;
}

.status-bar {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 20px;
  background: #f8f9fa;
  font-size: 14px;
  font-weight: 600;
  color: #000;
}

.time {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.signal-icons {
  display: flex;
  gap: 4px;
  align-items: center;
}

.signal, .wifi, .battery {
  width: 18px;
  height: 12px;
  background: #000;
  border-radius: 2px;
}

.signal {
  background: linear-gradient(to right, #000 0%, #000 60%, #ccc 60%);
}

.wifi {
  background: #000;
  border-radius: 50% 50% 0 0;
}

.battery {
  background: #4ade80;
  position: relative;
}

.battery::after {
  content: '';
  position: absolute;
  right: -2px;
  top: 3px;
  width: 2px;
  height: 6px;
  background: #000;
  border-radius: 0 1px 1px 0;
}

.app-header {
  position: relative;
  z-index: 2;
  background: #fff;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
}

.app-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  text-align: center;
}

.modules-container {
  position: relative;
  z-index: 2;
  background: #f8f9fa;
  height: calc(100% - 120px);
  overflow-y: auto;
}

.empty-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px 20px;
}

.bottom-nav {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
}

.nav-indicator {
  width: 134px;
  height: 5px;
  background: #000;
  border-radius: 3px;
}

/* 预览模式下的图标尺寸控制 */
.preview-icon :deep(svg) {
  width: 16px !important;
  height: 16px !important;
}

/* 预览网格布局 */
.auto-rows-min {
  grid-auto-rows: min-content;
}
</style>
