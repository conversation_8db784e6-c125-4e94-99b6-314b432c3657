<script setup lang="ts">
import { VueDraggable } from 'vue-draggable-plus'
import { ref } from 'vue'
const iconComponents = {
  "Bell": '<svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 19V20H3V19L5 17V11C5 7.9 7 5.2 10 4.3V4C10 2.3 11.3 1 13 1S16 2.3 16 4V4.3C19 5.2 21 7.9 21 11V17L23 19Z"/></svg>',
  "Mail": '<svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24"><path d="M20 4H4C2.9 4 2.01 4.9 2.01 6L2 18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6C22 4.9 21.1 4 20 4ZM20 8L12 13L4 8V6L12 11L20 6V8Z"/></svg>',
  "FileText": '<svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24"><path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/></svg>',
  "Navigation": '<svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24"><path d="M12,2A7,7 0 0,1 19,9C19,14.25 12,22 12,22C12,22 5,14.25 5,9A7,7 0 0,1 12,2M12,4A5,5 0 0,0 7,9C7,13 12,18.71 12,18.71C12,18.71 17,13 17,9A5,5 0 0,0 12,4M12,7A2,2 0 0,1 14,9A2,2 0 0,1 12,11A2,2 0 0,1 10,9A2,2 0 0,1 12,7Z"/></svg>',
  "Edit": '<svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24"><path d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"/></svg>',
  "Camera": '<svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24"><path d="M4,4H7L9,2H15L17,4H20A2,2 0 0,1 22,6V18A2,2 0 0,1 20,20H4A2,2 0 0,1 2,18V6A2,2 0 0,1 4,4M12,7A5,5 0 0,0 7,12A5,5 0 0,0 12,17A5,5 0 0,0 17,12A5,5 0 0,0 12,7M12,9A3,3 0 0,1 15,12A3,3 0 0,1 12,15A3,3 0 0,1 9,12A3,3 0 0,1 12,9Z"/></svg>',
  "MapPin": '<svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24"><path d="M12,11.5A2.5,2.5 0 0,1 9.5,9A2.5,2.5 0 0,1 12,6.5A2.5,2.5 0 0,1 14.5,9A2.5,2.5 0 0,1 12,11.5M12,2A7,7 0 0,0 5,9C5,14.25 12,22 12,22C12,22 19,14.25 19,9A7,7 0 0,0 12,2Z"/></svg>',
  "Download": '<svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24"><path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z"/></svg>',
  "Phone": '<svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24"><path d="M6.62,10.79C8.06,13.62 10.38,15.94 13.21,17.38L15.41,15.18C15.69,14.9 16.08,14.82 16.43,14.93C17.55,15.3 18.75,15.5 20,15.5A1,1 0 0,1 21,16.5V20A1,1 0 0,1 20,21A17,17 0 0,1 3,4A1,1 0 0,1 4,3H7.5A1,1 0 0,1 8.5,4C8.5,5.25 8.7,6.45 9.07,7.57C9.18,7.92 9.1,8.31 8.82,8.59L6.62,10.79Z"/></svg>',
  "Users": '<svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24"><path d="M12,5.5A3.5,3.5 0 0,1 15.5,9A3.5,3.5 0 0,1 12,12.5A3.5,3.5 0 0,1 8.5,9A3.5,3.5 0 0,1 12,5.5M5,8C5.56,8 6.08,8.15 6.53,8.42C6.38,9.85 6.8,11.27 7.66,12.38C7.16,13.34 6.16,14 5,14A3,3 0 0,1 2,11A3,3 0 0,1 5,8M19,8A3,3 0 0,1 22,11A3,3 0 0,1 19,14C17.84,14 16.84,13.34 16.34,12.38C17.2,11.27 17.62,9.85 17.47,8.42C17.92,8.15 18.44,8 19,8M5.5,18.25C5.5,16.18 8.41,14.5 12,14.5C15.59,14.5 18.5,16.18 18.5,18.25V20H5.5V18.25Z"/></svg>'
};
// 显示配置面板
const showConfig = ref(false);
const selectedModule = ref(null);

// 模块配置数据
const modules = ref([
  {
    id: 1,
    title: '会议通知',
    subtitle: 'Notification',
    icon: 'Bell',
    color: 'from-purple-400 to-purple-600',
    width: 50,
    sort: 1,
    hidden: false,
    height: 200
  },
  {
    id: 2,
    title: '参会报名',
    subtitle: 'Sign Up',
    icon: 'Mail',
    color: 'from-orange-400 to-orange-600',
    width: 50,
    sort: 2,
    hidden: false,
    height: 200
  },
  {
    id: 3,
    title: '会议议程',
    subtitle: 'Agenda',
    icon: 'FileText',
    color: 'from-pink-400 to-pink-600',
    width: 50,
    sort: 3,
    hidden: false,
    height: 400
  },
  {
    id: 4,
    title: '地图导航',
    subtitle: 'Navigation',
    icon: 'Navigation',
    color: 'from-cyan-400 to-cyan-600',
    width: 50,
    sort: 4,
    hidden: false,
    height: 400
  },
  {
    id: 5,
    title: '会议签到',
    subtitle: 'Sign In',
    icon: 'Edit',
    color: 'from-green-400 to-green-600',
    width: 50,
    sort: 5,
    hidden: false,
    height: 200
  },
  {
    id: 6,
    title: '图文直播',
    subtitle: 'Live Streaming',
    icon: 'Camera',
    color: 'from-pink-400 to-red-500',
    width: 50,
    sort: 6,
    hidden: false,
    height: 400
  },
  {
    id: 7,
    title: '座位图',
    subtitle: 'Seat Map',
    icon: 'MapPin',
    color: 'from-purple-500 to-indigo-600',
    width: 50,
    sort: 7,
    hidden: false,
    height: 200
  },
  {
    id: 8,
    title: '资料下载',
    subtitle: 'Download',
    icon: 'Download',
    color: 'from-red-400 to-red-700',
    width: 50,
    sort: 8,
    hidden: false,
    height: 200
  },
  {
    id: 9,
    title: '联系会务组',
    subtitle: 'Contact Us',
    icon: 'Phone',
    color: 'from-purple-500 to-purple-700',
    width: 50,
    sort: 9,
    hidden: false,
    height: 200
  },
  {
    id: 10,
    title: '我的信息',
    subtitle: 'My Information',
    icon: 'Users',
    color: 'from-green-400 to-green-600',
    width: 50,
    sort: 10,
    hidden: false,
    height: 200
  }
]);
const getColorClass = (color: string) => {
  return 'bg-gradient-to-br ' + color;
};
// 工具函数
const getWidthClass = (width: number) => {
  return width === 100 ? 'col-span-2' : 'col-span-1';
};

const getHeightClass = (height: number) => {
  return height === 400 ? 'row-span-2' : 'row-span-1';
};

const getHeightPx = (height: number) => {
  return height === 400 ? 'h-[416px]' : 'h-[200px]';
};

// 更新模块配置
const updateModule = (id: number, updates:any) => {
  const index = modules.value.findIndex(m => m.id === id);
  if (index !== -1) {
    modules.value[index] = { ...modules.value[index], ...updates };
  }
};

// 获取图标HTML
const getIconHtml = (iconName: string) => {
  return iconComponents[iconName] || '';
};

// 拖拽配置
const dragOptions = {
  animation: 150,
  ghostClass: 'sortable-ghost',
  chosenClass: 'sortable-chosen',
  dragClass: 'sortable-drag',
  forceFallback: true,
  handle: '.drag-handle'
};
</script>

<template>
  <div class="bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700">
    <div class="min-h-screen p-4">
      <!-- 标题栏 -->
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-white">会议模块布局 (Vue3 + VueDraggablePlus)</h1>
        <button
            @click="showConfig = !showConfig"
            class="bg-white/20 text-white px-4 py-2 rounded-lg hover:bg-white/30 transition-colors"
        >
          {{ showConfig ? '隐藏配置' : '显示配置' }}
        </button>
      </div>

      <div class="flex gap-6">
        <!-- 配置面板 -->
        <div v-if="showConfig" class="w-80 max-h-screen overflow-y-auto">
          <h2 class="text-xl font-bold text-white mb-4">模块配置</h2>

          <div v-for="module in modules" :key="module.id" class="bg-white p-4 rounded-lg shadow-lg mb-4">
            <h3 class="font-semibold mb-3">{{ module.title }} 配置</h3>

            <div class="space-y-3">
              <div>
                <label class="block text-sm font-medium mb-1">宽度</label>
                <select
                    :value="module.width"
                    @change="updateModule(module.id, { width: parseInt($event.target.value) })"
                    class="w-full border rounded px-3 py-2"
                >
                  <option value="50">50% (半宽)</option>
                  <option value="100">100% (全宽)</option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium mb-1">高度</label>
                <select
                    :value="module.height"
                    @change="updateModule(module.id, { height: parseInt($event.target.value) })"
                    class="w-full border rounded px-3 py-2"
                >
                  <option value="200">200px (标准高度)</option>
                  <option value="400">400px (双倍高度)</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- 主网格布局 -->
        <div class="flex-1">
          <VueDraggable
              v-model="modules"
              :options="dragOptions"
              class="grid grid-cols-2 gap-4 grid-auto-rows"
          >
            <div
                v-for="module in modules"
                :key="module.id"
                :class="`${getWidthClass(module.width)} ${getHeightClass(module.height)} ${getHeightPx(module.height)} ${getColorClass(module.color)}`"
                class="rounded-2xl p-6 text-white transform transition-all duration-300 shadow-xl hover:shadow-2xl hover:scale-102 flex flex-col justify-between group relative drag-handle"
                @click="selectedModule = module"
            >
              <!-- 拖拽指示器 -->
              <div class="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <div class="bg-black/30 text-white p-1 rounded">
                  <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
                    <circle cx="2" cy="2" r="1"/>
                    <circle cx="6" cy="2" r="1"/>
                    <circle cx="10" cy="2" r="1"/>
                    <circle cx="2" cy="6" r="1"/>
                    <circle cx="6" cy="6" r="1"/>
                    <circle cx="10" cy="6" r="1"/>
                    <circle cx="2" cy="10" r="1"/>
                    <circle cx="6" cy="10" r="1"/>
                    <circle cx="10" cy="10" r="1"/>
                  </svg>
                </div>
              </div>

              <!-- 模块内容 -->
              <div class="flex flex-col h-full justify-between pointer-events-none">
                <div>
                  <h3 class="text-xl font-bold mb-2">{{ module.title }}</h3>
                  <p class="text-white/80 text-sm">{{ module.subtitle }}</p>
                </div>

                <div class="flex justify-end">
                  <div class="bg-white/20 p-3 rounded-full group-hover:bg-white/30 transition-colors"
                       v-html="getIconHtml(module.icon)">
                  </div>
                </div>
              </div>

              <!-- 配置信息显示 -->
              <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <div class="bg-black/50 text-white text-xs px-2 py-1 rounded">
                  {{ module.width }}% × {{ module.height }}px
                </div>
              </div>
            </div>
          </VueDraggable>
        </div>
      </div>

      <!-- 模块详情弹窗 -->
      <div v-if="selectedModule"
           class="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50"
           @click="selectedModule = null"
      >
        <div class="bg-white rounded-xl p-6 max-w-md w-full" @click.stop>
          <h3 class="text-xl font-bold mb-2">{{ selectedModule.title }}</h3>
          <p class="text-gray-600 mb-4">{{ selectedModule.subtitle }}</p>
          <div class="space-y-2 text-sm text-gray-500">
            <p>宽度: {{ selectedModule.width }}%</p>
            <p>高度: {{ selectedModule.height }}px</p>
            <p>网格跨度: {{ selectedModule.width === 100 ? '2列' : '1列' }} × {{ selectedModule.height === 400 ? '2行' : '1行' }}</p>
          </div>
          <button
              @click="selectedModule = null"
              class="mt-4 bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors"
          >
            关闭
          </button>
        </div>
      </div>

      <!-- 说明文字 -->
      <div class="mt-8 text-white/70 text-sm">
        <p>• 模块宽度支持 50% (1列) 和 100% (2列)</p>
        <p>• 模块高度支持 200px (1行) 和 400px (2行)</p>
        <p>• 点击"显示配置"可以动态调整每个模块的尺寸</p>
        <p>• 鼠标悬停可查看模块当前配置信息</p>
        <p>• <strong>拖拽功能：</strong>使用 VueDraggablePlus 实现，悬停时显示拖拽图标</p>
      </div>
    </div>
  </div>

</template>

<style scoped>
.sortable-ghost {
  opacity: 0.4;
}
.sortable-chosen {
  transform: scale(1.02);
}
.sortable-drag {
  transform: rotate(5deg);
  opacity: 0.8;
}
.grid-auto-rows {
  grid-auto-rows: 200px;
}
.drag-handle {
  cursor: grab;
}
.drag-handle:active {
  cursor: grabbing;
}
</style>