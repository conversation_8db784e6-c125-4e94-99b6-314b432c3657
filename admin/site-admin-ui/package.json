{"name": "ruoyi", "version": "3.8.7", "description": "上海市核电办公室门户网站后台管理系统", "author": "若依", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build:prod": "vite build --mode production", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@element-plus/icons": "^0.0.11", "@element-plus/icons-vue": "2.3.1", "@tinymce/tinymce-vue": "^6.0.1", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.6.1", "activiti-bpmn-moddle": "^4.4.0", "axios": "0.27.2", "bpmn-js": "^17.9.1", "bpmn-js-properties-panel": "^5.19.0", "dom-to-image-more": "^3.5.0", "echarts": "5.4.3", "element-plus": "2.4.3", "file-saver": "2.0.5", "fuse.js": "6.6.2", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "lodash": "^4.17.21", "nprogress": "0.2.0", "pinia": "2.1.7", "quill-image-drop-module": "^1.0.3", "quill-image-resize-module-withfix": "^4.0.1", "ruoyi": "file:", "tailwind": "^4.0.0", "tree-lodash": "^0.4.0", "uuid": "^11.1.0", "vform3-builds": "^3.0.10", "vue": "3.3.9", "vue-cropper": "1.1.1", "vue-draggable-plus": "^0.6.0", "vue-qr": "^4.0.9", "vue-router": "4.2.5"}, "devDependencies": {"@types/lodash": "^4.17.7", "@vitejs/plugin-vue": "4.5.0", "@vue/compiler-sfc": "3.3.9", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "sass": "1.69.5", "tailwindcss": "^4.1.4", "unocss": "^0.61.5", "unplugin-auto-import": "0.17.1", "unplugin-vue-setup-extend-plus": "1.0.0", "vite": "5.0.4", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1"}}