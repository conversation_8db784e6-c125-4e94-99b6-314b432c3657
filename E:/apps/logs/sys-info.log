14:56:10.336 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.2.Final
14:56:10.424 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,53] - Starting RuoYiApplication using Java 21.0.2 with PID 59051 (/Users/<USER>/workspace/project2025/核电办门户/offical-site/admin/site-admin/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/核电办门户/offical-site)
14:56:10.425 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,658] - The following 1 profile is active: "test"
14:57:49.047 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.2.Final
14:57:49.105 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,53] - Starting RuoYiApplication using Java 21.0.2 with PID 59297 (/Users/<USER>/workspace/project2025/核电办门户/offical-site/admin/site-admin/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/核电办门户/offical-site)
14:57:49.107 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,658] - The following 1 profile is active: "test"
15:03:30.529 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.2.Final
15:03:30.587 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,53] - Starting RuoYiApplication using Java 21.0.2 with PID 60843 (/Users/<USER>/workspace/project2025/核电办门户/offical-site/admin/site-admin/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/核电办门户/offical-site)
15:03:30.589 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,658] - The following 1 profile is active: "test"
15:03:33.067 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
15:03:34.259 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8083"]
15:03:34.261 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:03:34.261 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.39]
15:03:34.307 [restartedMain] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring embedded WebApplicationContext
15:03:35.161 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
15:03:35.751 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
15:03:37.438 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1010] - {dataSource-1} inited
15:03:39.877 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:03:39.890 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:03:39.890 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:03:39.891 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:03:39.892 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:03:39.892 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:03:39.892 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:03:39.892 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6abd1676
15:03:44.158 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,54] - The following process definition files will be deployed: [diagram.bpmn]
15:03:45.790 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
15:03:45.961 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1276] - Found 1 Process Engine Configurators in total:
15:03:45.961 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1281] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
15:03:45.961 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1293] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
15:03:46.383 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1304] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
15:03:46.496 [restartedMain] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
15:03:46.498 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [start,173] - Starting up the default async job executor [org.activiti.spring.SpringAsyncExecutor].
15:03:46.499 [Thread-9] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,49] - {} starting to acquire async jobs due
15:03:46.499 [Thread-10] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,53] - {} starting to acquire async jobs due
15:03:46.499 [Thread-11] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,55] - {} starting to reset expired jobs
15:03:46.608 [restartedMain] INFO  o.a.e.i.c.DeployCmd - [executeDeploy,103] - Launching new deployment with version: 1
15:03:46.948 [restartedMain] INFO  o.a.e.i.b.d.BpmnDeployer - [dispatchProcessDefinitionEntityInitializedEvent,240] - Process deployed: {id: f9e1fe3e-8313-11f0-a58a-823edf915be7, key: news_release_audit_process, name: news_release_audit_process }
15:03:51.724 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8083"]
15:03:51.734 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
15:03:51.936 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
15:03:51.937 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
15:03:52.131 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
15:03:52.132 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
15:04:18.160 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
15:04:18.161 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
15:04:18.240 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
15:04:18.241 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:04:18.275 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,59] - Started RuoYiApplication in 48.136 seconds (process running for 50.094)
