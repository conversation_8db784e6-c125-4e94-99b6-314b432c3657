import { getColumnList as fetchColumn } from "~/api/column"

export enum ECOLUMN_CODE {
  INDEX = 'INDEX',
  ORG = 'ORG',
  /** 机构概况 */
  ORG_ABOUT = 'ORG_ABOUT',
  ORG_LEADER = 'ORG_LEADER',
  ORG_LEADER_DETAIL = 'ORG_LEADER_DETAIL',
  ORG_DEPT = 'ORG_DEPT',
  ORG_EVENT = 'ORG_EVENT',
  ORG_HONOR = 'ORG_HONOR',
  NEWS = 'NEWS',
  /** 党建动态 */
  NEWS_PARTY = 'NEWS_PARTY',
  /** 产业动态 */
  NEWS_INDUSTRY = 'NEWS_INDUSTRY',
  PARTY = 'PARTY',
  PARTY_UNION = 'PARTY_UNION',
  PARTY_UNION_ABOUT = 'PARTY_UNION_ABOUT',
  PARTY_UNION_AREA = 'PARTY_UNION_AREA',
  PARTY_UNION_AREA_DETAIL = 'PARTY_UNION_AREA_DETAIL',
  PARTY_UNION_MEMBER = 'PARTY_UNION_MEMBER',
  PARTY_UNION_NOTICE = 'PARTY_UNION_NOTICE',
  PARTY_UNION_RULE = 'PARTY_UNION_RULE',
  PARTY_UNION_TYPICAL = 'PARTY_UNION_TYPICAL',
  PARTY_UNION_WORK = 'PARTY_UNION_WORK',

  PARTY_SMART = 'PARTY_SMART',
  PARTY_SMART_ABOUT = 'PARTY_SMART_ABOUT',
  PARTY_SMART_RULE = 'PARTY_SMART_RULE',
  PARTY_SMART_EXPERT = 'PARTY_SMART_EXPERT',
  PARTY_SMART_EXPERT_DETAIL = 'PARTY_SMART_EXPERT_DETAIL',
  PARTY_SMART_NOTICE = 'PARTY_SMART_NOTICE',
  PARTY_SMART_WORK = 'PARTY_SMART_WORK',

  INDUSTRY = 'INDUSTRY',
  INDUSTRY_GLOBAL = 'INDUSTRY_GLOBAL',
  INDUSTRY_GLOBAL_DATA = 'INDUSTRY_GLOBAL_DATA',
  INDUSTRY_GLOBAL_NEWS = 'INDUSTRY_GLOBAL_NEWS',
  INDUSTRY_GLOBAL_RULE = 'INDUSTRY_GLOBAL_RULE',

  INDUSTRY_CHINA = 'INDUSTRY_CHINA',
  INDUSTRY_CHINA_DATA = 'INDUSTRY_CHINA_DATA',
  INDUSTRY_CHINA_NEWS = 'INDUSTRY_CHINA_NEWS',
  INDUSTRY_CHINA_RULE = 'INDUSTRY_CHINA_RULE',

  INDUSTRY_SHANGHAI = 'INDUSTRY_SHANGHAI',
  INDUSTRY_SHANGHAI_NEWS = 'INDUSTRY_SHANGHAI_NEWS',
  INDUSTRY_SHANGHAI_RULE = 'INDUSTRY_SHANGHAI_RULE',
  INDUSTRY_SHANGHAI_MAP = 'INDUSTRY_SHANGHAI_MAP',
  INDUSTRY_SHANGHAI_MAP_INDUSTRY = 'INDUSTRY_SHANGHAI_MAP_INDUSTRY',
  INDUSTRY_SHANGHAI_MAP_COMPANY = 'INDUSTRY_SHANGHAI_MAP_COMPANY',
  INDUSTRY_SHANGHAI_EVENT = 'INDUSTRY_SHANGHAI_EVENT',
  SCIENCE = 'SCIENCE',
  SCIENCE_DIGITAL = 'SCIENCE_DIGITAL',
  SCIENCE_DIGITAL_DETAIL = 'SCIENCE_DIGITAL_DETAIL',
  SCIENCE_MESEUM = 'SCIENCE_MESEUM',
  COMPANY = 'COMPANY',
  COMPANY_LOGIN = 'COMPANY_LOGIN',
  COMPANY_LOGIN_CA = 'COMPANY_LOGIN_CA',
  COMPANY_LOGIN_WX = 'COMPANY_LOGIN_WX',
  COMPANY_SERVICE_TRAIN_BEFORE_LOGIN = 'COMPANY_SERVICE_TRAIN_BEFORE_LOGIN',
  COMPANY_SERVICE_TRAIN = 'COMPANY_SERVICE_TRAIN',
  COMPANY_SERVICE_TRAIN_EDIT = 'COMPANY_SERVICE_TRAIN_EDIT',
  COMPANY_SERVICE_TRAIN_VIEW = 'COMPANY_SERVICE_TRAIN_VIEW',
  COMPANY_SERVICE_TRAIN_EVALUATION = 'COMPANY_SERVICE_TRAIN_EVALUATION',
  COMPANY_BEFORE_LOGIN='COMPANY_BEFORE_LOGIN',
  COMPANY_SERVICE_RULE_KNOWLEDGE = 'COMPANY_SERVICE_RULE_KNOWLEDGE',
  COMPANY_SERVICE_RULE_MONTHLY = 'COMPANY_SERVICE_RULE_MONTHLY',
  COMPANY_SERVICE_RULE_WEEKLY = 'COMPANY_SERVICE_RULE_WEEKLY',
  COMPANY_DATA_INDUSTRY_JOURNAL = 'COMPANY_DATA_INDUSTRY_JOURNAL',
  COMPANY_DATA_INDUSTRY_JOURNAL_BULLETIN = 'COMPANY_DATA_INDUSTRY_JOURNAL_BULLETIN',
  COMPANY_DATA_INDUSTRY_JOURNAL_REFERENCE = 'COMPANY_DATA_INDUSTRY_JOURNAL_REFERENCE',
  COMPANY_DATA_REPORT = 'COMPANY_DATA_REPORT',
  COMPANY_DATA_REPORT_RESULT = 'COMPANY_DATA_REPORT_RESULT',
  COMPANY_DATA_REPORT_YEAR = 'COMPANY_DATA_REPORT_YEAR',
  COMPANY_DATA_REPORT_QUARTER = 'COMPANY_DATA_REPORT_QUARTER',
  COMPANY_DATA_INDUSTRY_DIRECTORIES = 'COMPANY_DATA_INDUSTRY_DIRECTORIES',
  COMPANY_DATA_INDUSTRY_DETAIL = 'COMPANY_DATA_INDUSTRY_DETAIL',
  COMPANY_REPORT_DATAFILL = 'COMPANY_REPORT_DATAFILL',
  COMPANY_REPORT_MEETING = 'COMPANY_REPORT_MEETING',
  COMPANY_REPORT_OTHER = 'COMPANY_REPORT_OTHER',
  COMPANY_INFO_COMPANY = 'COMPANY_INFO_COMPANY',
  COMPANY_INFO_INDUSTRY = 'COMPANY_INFO_INDUSTRY',
  NOTICE = 'NOTICE',
  NOTICE_NOTICE = 'NOTICE_NOTICE',
  
}

export type columnTree = IColumn & { children: columnTree[] }

function buildTree(columnList: IColumn[]): columnTree[] {
  const tree: columnTree[] = []
  const map = new Map<string, columnTree>()

  // 将所有列存储到 map 中，方便快速查找
  for (const column of columnList) {
    map.set(column.id, { ...column, children: [] }) // 为每个列添加 children 属性
  }

  // 遍历所有列，构建树形结构
  for (const column of columnList) {
    if (column.columnPid === '0') {
      // 如果是根节点，直接添加到树中
      tree.push(map.get(column.id)!)
    } else {
      // 如果不是根节点，找到其父节点并添加到父节点的 children 中
      const parent = map.get(column.columnPid)
      if (parent) {
        parent.children.push(map.get(column.id)!)
      }
    }
  }

  // 递归排序树形结构的每一级
  function sortTree(nodes: columnTree[]): void {
    nodes.sort((a, b) => a.planceNo - b.planceNo) // 按 placeNo 升序排序
    for (const node of nodes) {
      if (node.children && node.children.length > 0) {
        sortTree(node.children) // 递归排序子节点
      }
    }
  }
  // 对根节点进行排序
  sortTree(tree)
  return tree
}

export const useColumnStore = defineStore('column', () => {
  const columnList = ref<IColumn[]>([])
  const columnTree = ref<columnTree[]>([])
  const fetchColumnList = async () => {
    const { data } = await fetchColumn()
    columnList.value = data ?? []
    columnTree.value = buildTree(columnList.value)
  }

  const getColumnList = computed(() => {
    return columnList.value
  })

  const getLevelOneColumnList = computed(() => {
    return columnList.value.filter((item) => { return item.columnPid === '0' }).sort((a, b) => { return a.planceNo - b.planceNo })
  })

  const getColumnIdByColumnCode = (columnCode: string) => {
    return columnList.value.find((item) => { return item.columnCode === columnCode })?.id
  }

  const getColumnByColumnCode = (columnCode: string) => {
    return columnList.value.find((item) => { return item.columnCode === columnCode })
  }

  const getColumnByColumnPcUrl = (columnPcUrl: string) => {
    const config = useRuntimeConfig()
    return columnList.value.find((item) => { return item.columnPcUrl === columnPcUrl.replace(config.public.baseUrl, '') })
  }

  const getColumnTree = computed(() => {
    return columnTree.value
  })

  return {
    fetchColumnList,
    getColumnList,
    getColumnTree,
    getLevelOneColumnList,
    getColumnIdByColumnCode,
    getColumnByColumnCode,
    getColumnByColumnPcUrl
  }
})