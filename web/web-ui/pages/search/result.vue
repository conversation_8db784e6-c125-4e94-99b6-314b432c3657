<template>

  <div class="pt-2 flex justify-center pb-[150px]">
     <!-- 加载动画组件 -->
    <div   class="web1260 bg_b p-1">
      <section class="h-[38px] w-full flex from-[#FAFAFA] to-[#F4F4F4] bg-gradient-to-b text-sm leading-[38px]">
        <div class="ml-5">
          <i class="iconfont icon-shouye1 text-[#318AD9]"></i>
        </div>
        <div>
          <div class="pl-[3px]">
            当前位置：
            <span>
              <a class="text-[#318AD9]">搜索结果</a>

            </span>
          </div>
        </div>
      </section>
      <div class="mt-[8px] flex gap-3 min-h-[1000px] ">
        <section class="bg_left w-[270px]">
          <div class="bg_top1" />
        </section>
        <div class="flex-grow">
          <div class="ml-[16px] overflow-hidden pr-[12px]">
            <div class="banner">
              <!-- <img src="/images/searchbg.png"> -->
              <img src="/public/images/searchbg.png"
            </div>
            <div class="px-[20px] py-[20px]">
              <div class="text-left">
                 <div v-if="isLoading" class=" bg-white bg-opacity-80 z-50 flex items-center justify-center mt-[100px]">
      <div class="loader animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
    </div>
    <div  v-else>
                <div class="mx-[5px] mt-[10px] bg-[#fafafa] py-[8px] px-[15px]">找到了 <span class="text-[#318AD9]">{{
                  total }}</span> 条“<span class="text-[#dc352f]">{{ getTitle }}</span>”的相关内容</div>
                <div v-for="item in releaseList" :key="item.id"
                  class="relative flex justify-between border-b border-[#eee] mt-[20px] px-[5px] py-[12px] transition ease-in hover:bg-[#f3f9ff]"
                  @click="$router.push({ name: 'RELEASE', params: { id: item.id, type: 'a' } })">
                  <span class="absolute top-[21px] h-[5px] w-[5px] bg-[#4792CD]" />
                  <h1 class="ml-[15px] w-[70%]">
                    <a class="line-clamp-1 cursor-pointer text-[16px]">{{ item.title }}</a>
                  </h1>
                  <div class="text-sm">
                    <span class="ml-[4px] text-gray-400">
                      <i class="iconfont icon-shijian" />
                      {{ dateFormat(item.articleDate) }}
                    </span>
                  </div>
                </div>
                <div class="mt-[20px] flex justify-center">
                  <the-pagination :total="total" v-model:page="searchForm.pageNum" v-model:limit="searchForm.pageSize"
                    @pagination="search" layout="prev, pager, next"></the-pagination>
                </div>
      </div>
              </div>
            </div>
          </div>
        </div>

      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
import { getReleaseList } from '~/api/release'
import { ECOLUMN_CODE, useColumnStore } from '~/store/column'

definePageMeta({
  name: 'SEARCH_RESLUT',
})
const columnStore = useColumnStore()
const getTitle = computed(() => {
  return useRoute().query.title as string
})
const isLoading = ref(true);
const total = ref(0)
const searchForm = reactive({
  pageNum: 1,
  pageSize: 10,
})
const releaseList = ref<IRelease[]>([])
const search = () => {
  isLoading.value = true; // 请求开始显示加载
  getReleaseList({
    title: getTitle.value,
    columnIdList: [
      columnStore.getColumnIdByColumnCode(ECOLUMN_CODE.NEWS_INDUSTRY)??'',
      columnStore.getColumnIdByColumnCode(ECOLUMN_CODE.NEWS_PARTY)??'',
      columnStore.getColumnIdByColumnCode(ECOLUMN_CODE.PARTY_UNION_TYPICAL)??'',
      columnStore.getColumnIdByColumnCode(ECOLUMN_CODE.PARTY_UNION_WORK)??'',
      columnStore.getColumnIdByColumnCode(ECOLUMN_CODE.PARTY_UNION_NOTICE)??'',
      columnStore.getColumnIdByColumnCode(ECOLUMN_CODE.PARTY_SMART_NOTICE)??'',
      columnStore.getColumnIdByColumnCode(ECOLUMN_CODE.PARTY_SMART_WORK)??'',

      columnStore.getColumnIdByColumnCode(ECOLUMN_CODE.INDUSTRY_GLOBAL_NEWS)??'',
      columnStore.getColumnIdByColumnCode(ECOLUMN_CODE.INDUSTRY_GLOBAL_RULE)??'',
      columnStore.getColumnIdByColumnCode(ECOLUMN_CODE.INDUSTRY_CHINA_NEWS)??'',
      columnStore.getColumnIdByColumnCode(ECOLUMN_CODE.INDUSTRY_CHINA_RULE)??'',
      columnStore.getColumnIdByColumnCode(ECOLUMN_CODE.INDUSTRY_SHANGHAI_NEWS)??'',
      columnStore.getColumnIdByColumnCode(ECOLUMN_CODE.INDUSTRY_SHANGHAI_RULE)??'',
    ]
  }, searchForm).then((res) => {
    total.value = res.total ?? 0
    releaseList.value = res.rows ?? []
  }).finally(() => {
    isLoading.value = false; // 请求完成隐藏
  });
}
search()
watch(getTitle, () => search())
</script>
<style scoped>
/* .bg_left {
  background: url(/images/bg_qy.png) no-repeat bottom;
  background-size: cover;
} */

.bg_left {
  background: url(/images/leftbg.png) no-repeat bottom;
}

.bg_top1 {
  background: url(/images/searchbg_l.png) no-repeat top;
  width: 270px;
  height: 70px;
  color: #fff;
}

.bg_left a {
  background: url(/images/Line.png) no-repeat bottom;
  text-align: left;
  padding-left: 50px;
  padding-top: 12px;
  padding-bottom: 12px;
  width: 100%;
  transition: all 0.4s ease;
  color: #5a5a5a;
}

.bg_left span {
  background: url(/images/arrow.png) no-repeat left center;
  background-size: 19px 18px;
  display: inline-block;
  text-align: left;
  padding-left: 25px;
  transition: all 0.4s ease;
}

.bg_left a:hover {
  background: url(/images/left_hover.png) no-repeat bottom;
  color: #479ee4;
}

.bg_left a:hover span {
  background: url(/images/arrow_hover.png) no-repeat left center;
}

.bg_left a.hover {
  background: url(/images/left_hover.png) no-repeat bottom;
  color: #479ee4;
}

.bg_left a.hover span {
  background: url(/images/arrow_hover.png) no-repeat left center;
}


/* 加载动画样式 */
.loader {
  animation: spin 1s linear infinite;
}
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>