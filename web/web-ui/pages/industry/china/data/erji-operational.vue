<template>
  <div
    :class="{ 'px-[20px] py-[20px]': !isFullscreen }"
    class="box mb-[150px] erji-operational"
    :style="{ transform: isFullscreen ? `scale(1)` : 'scale(1)' }"
  >
    <div
      class="bodybg relative"
      :style="{ height: isFullscreen ? `880px` : '870px' }"
    >
      <div class="topbg_industry mx-auto w-full flex justify-center">
        <h1 class="ys_font mt-[2px] text-[30px] text-white tracking-[10px]">
          中国核电
        </h1>
        <div
          class="backbg absolute right-[150px] top-[10px] mr-[15px] cursor-pointer"
        >
          <a
            class="text-[#fff] hover:color-[#318AD9] cursor-pointer"
            @click="toggleFullscreen()"
          >
            <span v-if="!isFullscreen">全屏</span>
            <span v-else>退出全屏</span>
          </a>
        </div>
        <div class="backbg absolute right-[30px] top-[10px] cursor-pointer">
          <a class="text-[#fff] hover:color-[#318AD9]" @click="$router.back()"
            >返回</a
          >
        </div>
      </div>
      <div class="flex-container mt-[10px] justify-center">
        <div class="flex-item">
          <h1
            class="ys_font mt-[10px] text-[22px] text-[#4F5D69] font-500 text-center"
          >
            在运核电机组统计
          </h1>
          <el-form
            :inline="true"
            class="flex justify-center demo-form-inline mt-[10px] border border-[#b6e3f9] bg-[rgba(217,242,255,0.4)] pt-[5px] pl-[10px]"
          >
            <el-form-item label="业主单位">
              <el-select
                v-model="searchForm.unitOwner"
                placeholder="请输入业主单位"
                filterable
                clearable
              >
                <el-option
                  v-for="item in list_dw"
                  :label="item.value!"
                  :value="item.value!"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="厂址">
              <el-input
                v-model="searchForm.unitAddress"
                placeholder="请输入厂址"
              />
            </el-form-item>
            <el-form-item label="堆型">
              <!-- <el-input v-model="searchForm.unitModel" placeholder="请输入堆型" /> -->
              <el-select
                v-model="searchForm.unitModel"
                placeholder="请输入堆型"
                filterable
                clearable
              >
                <el-option
                  v-for="item in list_dx"
                  :label="item.value!"
                  :value="item.value!"
                />
              </el-select>
            </el-form-item>

            <el-form-item>
              <!--                 :icon="Search" -->
              <el-button
                type="primary"
                class="custom-height-btn"
                @click="search"
              >
                查询
              </el-button>
            </el-form-item>
          </el-form>
          <el-table
            :data="list"
            height="665"
            :style="{ 'font-size': isFullscreen ? `15px` : '15px' }"
            :span-method="handleSpan"
            :row-class-name="getRowClassName"
            :cell-class-name="getCellClassName"
            border
            stripe
          >
            <el-table-column type="index" width="70" label="序号" header-align="center" align="center" />
            <el-table-column
              prop="unitAddress"
              label="厂址"
              show-overflow-tooltip
              :tooltip-content="getTooltipContent"
            />
            <el-table-column
              prop="unitName"
              label="机组名称"
              show-overflow-tooltip
            />
            <el-table-column prop="unitModel" label="堆型" />

            <el-table-column prop="unitCapacity" label="装机容量(MW)" />
            <el-table-column prop="unitOwner" label="业主单位" />
            <el-table-column
              prop="startTime"
              label="开工日期"
              show-overflow-tooltip
            />
            <el-table-column
              prop="firstTime"
              label="首次并网"
              show-overflow-tooltip
            />
            <el-table-column
              prop="businessTime"
              label="商业运行"
              show-overflow-tooltip
            />
          </el-table>
          <div class="absolute bottom-[15px] text-[14px]"  :style="{ right: isFullscreen ? `90px` : '44px' }">
            数据来源于IAEA、中国核能行业协会，截至{{
              lastUpdateDate
            }}(在运核电机组以首次装料为统计口径)
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
// import { Search } from '@element-plus/icons-vue'
import { reactive, ref } from 'vue'
import {
  getChinaOperList,
  getChinaDict,
  getChinaUpdateDeadline,
  type IChinaOperList,
  type Operational,
} from '@/api/crawler/china'
// import { useFullscreenLayout } from '@/composables/useFullscreenLayout'

import type { SpanMethodProps } from 'element-plus'
const { isFullscreen, toggleFullscreen } = useFullscreenLayout()
const spanArr = ref<number[]>([])
const rowColorMap = ref<Record<number, string>>({})
declare module 'element-plus' {
  export interface SpanMethodProps {
    row: Record<string, any>
    column: Record<string, any>
    rowIndex: number
    columnIndex: number
  }
}
const getBaseRowIndex = (rowIndex: number) => {
  // 查找当前行所属的合并组起始行
  for (let i = rowIndex; i >= 0; i--) {
    if (spanArr.value[i] > 0) return i
  }
  return rowIndex
}

// 计算行背景色 - 每个合并区域一种颜色，相邻区域交替
const getRowBackgroundColor = (rowIndex: number) => {
  const baseIndex = getBaseRowIndex(rowIndex)

  // 如果颜色缓存中没有，则计算并缓存
  if (!rowColorMap.value[baseIndex]) {
    // 计算这是第几个合并组（从0开始）
    let groupIndex = 0
    for (let i = 0; i < baseIndex; i++) {
      if (spanArr.value[i] > 0) {
        groupIndex++
      }
    }

    // 根据合并组索引交替使用两种颜色
    const color = groupIndex % 2 === 0 ? '#edf6ff' : '#ffffff'
    rowColorMap.value[baseIndex] = color
  }

  return rowColorMap.value[baseIndex]
}

// 行样式计算
const handleRowStyle = ({ rowIndex }: { rowIndex: number }) => {
  const backgroundColor = getRowBackgroundColor(rowIndex)
  return { backgroundColor }
}

// 获取悬浮提示内容
const getTooltipContent = (row: IChinaOperList, column: any) => {
  if (column.property === 'unitAddress') {
    const baseIndex = getBaseRowIndex(list.value.indexOf(row))
    const spanCount = spanArr.value[baseIndex]
    if (spanCount > 1) {
      // 获取该合并组的所有机组信息
      const groupUnits = list.value.slice(baseIndex, baseIndex + spanCount)
      const unitNames = groupUnits.map((unit) => unit.unitName).join('、')
      return `厂址：${row.unitAddress}\n包含机组：${unitNames}\n机组数量：${spanCount}个`
    }
  }
  return row[column.property as keyof IChinaOperList]
}

// 行类名计算
const getRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  const baseIndex = getBaseRowIndex(rowIndex)
  const backgroundColor = getRowBackgroundColor(rowIndex)

  // 根据背景色返回对应的类名
  if (backgroundColor === '#edf6ff') {
    return 'even-group-row'
  } else {
    return 'odd-group-row'
  }
}

// 单元格类名计算（针对合并单元格）
const getCellClassName = ({
  rowIndex,
  columnIndex,
}: {
  rowIndex: number
  columnIndex: number
}) => {
  const baseIndex = getBaseRowIndex(rowIndex)

  // 为合并单元格添加特殊类名，背景色与行背景色一致
  if (columnIndex === 1 && spanArr.value[baseIndex] > 0) {
    const backgroundColor = getRowBackgroundColor(rowIndex)
    return backgroundColor === '#edf6ff'
      ? 'merged-cell-even'
      : 'merged-cell-odd'
  }

  // 为所有单元格添加组类名，确保背景色一致
  const backgroundColor = getRowBackgroundColor(rowIndex)
  return backgroundColor === '#edf6ff' ? 'even-group-cell' : 'odd-group-cell'
}
// 计算合并行数的方法
const computeSpans = () => {
  const arr: number[] = []
  if (list.value.length === 0) return arr

  // 初始化数组，每行默认1行
  arr.push(1)
  let position = 0

  // 从第二行开始遍历
  for (let i = 1; i < list.value.length; i++) {
    // 当前行厂址与上一行相同
    if (list.value[i].unitAddress === list.value[i - 1].unitAddress) {
      arr[position] += 1 // 增加合并行数
      arr.push(0) // 当前行标记为0（不显示）
    } else {
      arr.push(1) // 不同厂址，新合并组
      position = i // 更新位置指针
    }
  }
  return arr
}
// 合并单元格方法
const handleSpan = ({
  row,
  column,
  rowIndex,
  columnIndex,
}: SpanMethodProps) => {
  // 只在厂址列（第1列）应用合并
  if (columnIndex === 1) {
    const rowspan = spanArr.value[rowIndex]
    return {
      rowspan: rowspan,
      colspan: rowspan > 0 ? 1 : 0,
    }
  }
  return { rowspan: 1, colspan: 1 }
}
const searchForm_dw = reactive({
  status: '1',
  type: 'yzdw',
} as Operational)
const list_dw = ref<Operational[]>([])
const searchForm_dx = reactive({
  status: '1',
  type: 'dx',
} as Operational)
const list_dx = ref<Operational[]>([])

const searchForm = reactive({
  searchDate: '',
} as IChinaOperList)
const list = ref<IChinaOperList[]>([])
const search = () => {
  getChinaOperList(searchForm).then((res) => {
    list.value = res.data ?? []
    spanArr.value = computeSpans() // 计算合并信息
    rowColorMap.value = {} // 清空颜色缓存
  })
  getChinaDict(searchForm_dw).then((res) => {
    list_dw.value = res.data ?? []
  })
  getChinaDict(searchForm_dx).then((res) => {
    list_dx.value = res.data ?? []
  })
}
search()
const lastUpdateDate = ref('')
getChinaUpdateDeadline().then((res) => {
  lastUpdateDate.value = res.msg
})
</script>
<style scoped>
/* 设置CSS变量 - 使用全局作用域 */
:deep(.erji-operational) {
  --even-group-row-color: #edf6ff;
  --odd-group-row-color: #ffffff;
}

/* 备用方案：直接在样式中设置变量 */
:deep(.el-table) {
  --even-group-row-color: #edf6ff;
  --odd-group-row-color: #ffffff;
}

.bodybg {
  background: url(/images/zyfw_bg1.png) no-repeat center 76px #edf6ff;
  background-size: cover;
  min-width: 320px;
  /* 改为移动端友好 */
  overflow: hidden;
}

.box {
  transform: scale(1);
  transform-origin: 0 0;
}

.topbg_industry {
  background: url(/images/zyfw_topbg.png) no-repeat top;
  background-size: cover;
  height: 55px;
  width: 100%;
}

.backbg {
  background: url(/images/zyfw_back.png) no-repeat top;
  background-size: contain;
  width: 155px;
  height: 26px;
  color: #fff;
  text-align: center;
  font-size: 16px;
  line-height: 27px;
}

.b_l {
  padding-bottom: 12px;
  border-bottom: 3px solid;
  border-image: linear-gradient(to right, #36b0ff, #0080d4) 1;
  font-weight: 500;
}

.left {
  width: 28.5rem;
}

:deep(.el-table) {
  background: rgba(255, 255, 255, 0.3);
}

:deep(.el-table .cell) {
  color: #303133;
}

:deep(.el-table .el-table__cell) {
}

:deep(.el-table td.el-table__cell) {
  border-right: solid 1px #fff;
}

:deep(.el-table th.el-table__cell .cell) {
  background-color: #8bd3f7;
  color: #fff;
  padding: 6px 0;
  padding-left: 5px;
}

:deep(.el-table .cell) {
  color: #212121;
  padding-left: 5px;
  white-space: nowrap;
  word-break: keep-all;
}

:deep(.el-table tr) {
  background-color: rgba(255, 255, 255, 0.5);
}

:deep(
    .el-table--striped
      .el-table__body
      tr.el-table__row--striped
      td.el-table__cell
  ) {
  background-color: rgba(220, 243, 251, 0.5);
}

.h-20 {
  height: 20rem;
}

.flex-container {
  display: flex;
  /* flex-wrap: wrap; */
  gap: 1rem;
}

.flex-item {
  flex: 0 0 90%;
  /* 最小320px */
  min-width: 450px;
  /* 移动端友好 */
}

@media (max-width: 1024px) {
  .main {
    /* order: 0; 中间内容优先显示 */
    flex-basis: 100%;
  }
}

:deep(.demo-form-inline .el-select) {
  --el-select-width: 150px;
}

:deep(.custom-date-picker) {
  width: 150px;
  --el-date-editor-width: 100%;
  /* 覆盖内部变量 */
  height: 26px;
  font-size: 12px;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 10px;
  margin-bottom: 5px;
}

:deep(.el-select__wrapper) {
  min-height: 26px;
  font-size: 12px;
  padding: 1px 12px;
}

.custom-height-btn {
  background: linear-gradient(90deg, #36b0ff 0%, #0080d4 100%);
  height: 26px;
  line-height: 26px;
  /* 确保文字垂直居中 */
  padding: 0 10px;
  /* 调整左右内边距 */
  font-size: 12px;
}

.custom-height-btn:hover {
  background: linear-gradient(90deg, #159af0 0%, #0080d4 100%);
}

:deep(.el-input__wrapper) {
  height: 26px;
}

:deep(.el-form-item__label) {
  padding-right: 5px;
  font-size: 16px;
}

:deep(.custom-height-btn) {
  font-size: 14px;
}
:deep(.el-select__input) {
  font-size: 16px;
}
:deep(.merged-cell-even) {
  background-color: var(--even-group-row-color) !important;
  font-weight: 500;

}

:deep(.merged-cell-odd) {
  background-color: var(--odd-group-row-color) !important;
  font-weight: 500;
  border-right: 1px solid #c3e6ff !important;
}

:deep(.even-group-row) {
  background-color: var(--even-group-row-color) !important;
}

:deep(.odd-group-row) {
  background-color: var(--odd-group-row-color) !important;
}

:deep(.even-group-cell) {
  background-color: var(--even-group-row-color) !important;
}

:deep(.odd-group-cell) {
  background-color: var(--odd-group-row-color) !important;
}
:deep(.el-table--border th.el-table__cell) {
  border: none !important;
}
:deep(.el-table td.el-table__cell){border-right: solid 1px #d8e9f0;}
</style>
