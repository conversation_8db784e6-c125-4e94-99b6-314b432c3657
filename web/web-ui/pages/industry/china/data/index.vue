<template>
  <div :class="{ 'px-[20px] py-[20px]': !isFullscreen }">
    <div class="bodybg relative mb-[120px]" :style="{ 'height': isFullscreen ? `830px` : '660px' }">
      <div class="topbg_industry  mx-auto w-full flex justify-center">
        <h1 class="ys_font mt-[2px] text-[30px] text-white tracking-[10px]">
          中国核电
        </h1>
        <div class="backbg absolute right-[30px] top-[10px]">
          <a class="text-[#fff] hover:color-[#318AD9] cursor-pointer" @click="toggleFullscreen()">
            <span v-if="!isFullscreen">全屏</span>
            <span v-else>退出全屏</span>
          </a>
        </div>
      </div>
      <div class="flex-container mt-[10px] px-[15px] pb-[15px] box"
        :style="{ transform: isFullscreen ? `scale(1)` : 'scale(0.76)' }">
        <div class="flex-item1">
          <div class="flex-container">
            <div class="flex-item h-[324px] overflow-hidden bg-[rgba(255,255,255,0.5)]">
              <h1 class="zyfw_menubg ys_font text-[18px]">
                装机容量
              </h1>
              
                <el-form  class=" mt-[20px] ml-[30px] mt-[10px]">
                  <el-form-item label="年度" label-width="70px">
                    <el-date-picker v-model="date" value-format="YYYY" type="year" placeholder="2025" clearable
                      class="custom-date-picker " style='width:80%' />
                  </el-form-item>
                  <el-form-item label="截止时间" label-width="70px">
                    <el-select v-model="quarter" placeholder="12月31日" size="large" style="width:80%">
                      <el-option v-for="item in quarterOptions1" :label="item.label" :value="item.value"
                        :disabled="item.disabled" />
                    </el-select>
                  </el-form-item>
                </el-form>
            
              <div class="din_font h-[80px] flex items-center justify-center">
                <!-- 当数据存在且非空时渲染 -->
                <template v-if="detail?.suppliedTotalGwh !== null && detail?.suppliedTotalGwh !== undefined">
                  <!-- 原数据渲染逻辑 -->
                  <template v-for="item in String(detail.suppliedTotalGwh).split('.').at(0)">
                    <span class="bg_sz">{{ item }}</span>
                  </template>
                  <template v-if="String(detail.suppliedTotalGwh).split('.').at(1)">
                    <em class="mx-[5px] h-[6px] w-[6px] bg-[#0AC7FF]" />
                    <template v-for="item in String(detail.suppliedTotalGwh).split('.').at(1)">
                      <span class="bg_sz">{{ item }}</span>
                    </template>
                  </template>
                </template>
                <!-- 数据为空时显示提示 -->
                <template v-else>
                  <span class="din_font gradient-text">0</span>
                </template>
              </div>
              <div class="flex justify-center">
                <span class="csjb">M</span><span class="csjb">w</span><span class="csjb">e</span>
              </div>
            </div>
            <div class="flex-item min-h-[220px] overflow-hidden bg-[rgba(255,255,255,0.5)]">
              <h1 class="zyfw_menubg ys_font text-[18px]">
                发电量
              </h1>
              <el-form  class=" mt-[20px] ml-[30px]">
                <el-form-item label="年度" label-width="70px">
                  <el-date-picker v-model="date" value-format="YYYY" type="year" placeholder="2025" clearable
                    class="custom-date-picker " style="width:80%" />
                </el-form-item>
                <el-form-item label="时间区间" label-width="70px">
                  <el-select v-model="quarter" placeholder="1-3月" size="large" style="width:80%">
                    <el-option v-for="item in quarterOptions" :label="item.label" :value="item.value"
                      :disabled="item.disabled" />
                  </el-select>
                </el-form-item>
              </el-form>
              <div class="bg_dz h-[130px] flex">
                <div class="ml-[20px]">
                  <img src="/images/zyfw_icon1.png" style="object-fit: cover; ">
                </div>
                <div class="ml-[5px]">
                  <p class="din_font gradient-text">
                    {{ detail?.nuclearGeneration ?? 0 }}<span class="text-[12px]">TWh</span>
                  </p>
                  <p>核电发电占比：{{ detail?.nuclearProportion ?? 0 }}%</p>
                </div>
              </div>
            </div>
            
          </div>
          <div
            class="mt-[15px] h-[390px] overflow-hidden overflow-hidden bg-[rgba(255,255,255,0.5)] p-[15px] text-center relative">
            <img src="/images/image-dt.png"
                style="object-fit:contain;height:360px ;">
            
          </div>
        </div>

        <div class="flex-item main">
          <div class="flex-container">
            <div class="flex-item overflow-hidden bg-[rgba(255,255,255,0.5)] w-[245px]">
              <h2 class="bg_menu flex justify-between">
                <div class="flex">
                  <img src="/images/zyfz_icon.png" class="ml-[10px] mt-[8px] h-[17px] w-[15px]"><span
                    class="ys_font ml-[5px] mt-[3px] text-[18px]"
                    :style="{ 'font-size': isFullscreen ? `18px` : '18px' }">在运情况</span>
                </div><a class="mr-[20px] mt-[6px] text-sm color-[#469EE4] hover:color-[#15aff9] cursor-pointer"
                  @click="$router.push({ path: '/industry/china/data/erji-operational' })"
                  :style="{ 'font-size': isFullscreen ? `14px` : '14px' }">更多>></a>
              </h2>
              <el-table :data="list2" stripe height="360" style="width: 100%"
                :style="{ 'font-size': isFullscreen ? `15px` : '15px' }">
                <el-table-column prop="unitName" label="机组名称" show-overflow-tooltip   min-width="100px"/>
                <el-table-column prop="unitModel" label="机组型号"  show-overflow-tooltip />
                <el-table-column prop="unitCapacity" label="装机" min-width="70px"/>
                <!-- <el-table-column prop="unitOwner" label="业主单位" show-overflow-tooltip  /> -->
              </el-table>
            </div>
            <div class="flex-item overflow-hidden bg-[rgba(255,255,255,0.5)] w-[245px]">
              <h2 class="bg_menu flex justify-between">
                <div class="flex">
                  <img src="/images/zyfz_icon.png" class="ml-[10px] mt-[8px] h-[17px] w-[15px]"><span
                    class="ys_font ml-[5px] mt-[3px] text-[18px]"
                    :style="{ 'font-size': isFullscreen ? `18px` : '18px' }">在建(核准待开工)情况</span>
                </div><a class="mr-[5px] mt-[6px] text-sm color-[#469EE4] hover:color-[#15aff9] cursor-pointer"
                  @click="$router.push({ path: '/industry/china/data/erji-construction' })"
                  :style="{ 'font-size': isFullscreen ? `14px` : '14px' }">更多>></a>
              </h2>
              <el-table :data="list1" stripe height="360" style="width: 100%"
                :style="{ 'font-size': isFullscreen ? `15px` : '15px' }">
                <el-table-column header-align="left" prop="unitName" label="机组名称" show-overflow-tooltip  min-width="100px"/>
                <el-table-column header-align="left" prop="unitModel" label="机组型号" show-overflow-tooltip />
                <el-table-column header-align="left" prop="unitCapacity" label="装机" min-width="70px"/>
                <!-- <el-table-column prop="unitOwner" label="业主单位" show-overflow-tooltip  /> -->
              </el-table>
            </div>
          </div>
          <div class="flex-item mt-[15px] h-20 overflow-hidden bg-[rgba(255,255,255,0.5)]">
            <h2 class="bg_menu mt-[10px] flex justify-between">
              <div class="flex">
                <img src="/images/zyfz_icon.png" class="ml-[10px] mt-[8px] h-[17px] w-[15px]">
                <span class="ys_font ml-[5px] mt-[3px] text-[18px]"
                  :style="{ 'font-size': isFullscreen ? `18px` : '21px' }">年度核准核电机组</span>
              </div><a class="mr-[20px] mt-[6px] text-sm color-[#469EE4] hover:color-[#15aff9] cursor-pointer"
                @click="$router.push({ path: '/industry/china/data/erji-annual' })"
                :style="{ 'font-size': isFullscreen ? `14px` : '14px' }">更多>></a>
            </h2>
            <div class="flex ">
              <div class=" w-[45%]"><div ref="chartRef" class="p-[10px] h-full">
              </div></div>
              <div class="bg-[rgba(255,255,255,0.5)] ml-[10px] w-[55%]">
                <el-table :data="list3" stripe height="260" style="width: 100%"
                  :default-sort="{ prop: 'year', order: 'descending' }"
                  :style="{ 'font-size': isFullscreen ? `15px` : '15px' }">
                  <el-table-column prop="year" label="年度" width="80px" />
                  <el-table-column prop="unitsCount" label="数量" width="70px" />
                  <el-table-column prop="unitsNames" label="名称" show-overflow-tooltip />
                </el-table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="absolute bottom-[10px] right-[20px] text-[14px]">数据来源于IAEA、中国核能行业协会，截至{{ lastUpdateDate }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { NuxtImg } from '#components';
import { reactive } from 'vue'
import { getChinaApproveList, getChinaOper, getChinaOperCheck, getChinaOperList, getChinaUnderList, getChinaUpdateDeadline, type IChinaApproveList, type IChinaOper, type IChinaUnderList } from '~/api/crawler/china'
import { ECOLUMN_CODE } from '~/store/column';
import * as echarts from 'echarts'
import dayjs from 'dayjs';

definePageMeta({
  name: ECOLUMN_CODE.INDUSTRY_CHINA_DATA,
  layout: 'column',

})
const { isFullscreen, toggleFullscreen, exitFullscreen } = useFullscreenLayout()

const date = ref<string>(String(dayjs().year()));
const quarter = ref<string>('1');
// 季度选择器选项
const quarterOptions = ref([
  { value: '1', label: '1-3月', disabled: false },
  { value: '2', label: '1-6月', disabled: false },
  { value: '3', label: '1-9月', disabled: false },
  { value: '4', label: '1-12月', disabled: false }
]);
const quarterOptions1 = ref([
  { value: '1', label: '3月31日', disabled: false },
  { value: '2', label: '6月30日', disabled: false },
  { value: '3', label: '9月30日', disabled: false },
  { value: '4', label: '12月31日', disabled: false }
]);

const getCheck = async () => {
  const res = await getChinaOperCheck(date.value)
  res.data?.forEach((item, index) => {
    quarterOptions.value.at(index)!.disabled = !item.result
    quarterOptions1.value.at(index)!.disabled = !item.result
  })
}
await getCheck()
watch(date, async () => {
  await getCheck()
})

const list1 = ref<IChinaUnderList[]>([])
const list2 = ref<IChinaUnderList[]>([])

const detail = ref<IChinaOper>()

for (let i = 3; i >= 0; i--) {
  if (quarterOptions.value.at(i)?.disabled == false) {
    quarter.value = quarterOptions.value.at(i)?.value ?? '1'
    break
  }
}
// 获取数据方法
const fetchData = async (year: string, type: string) => {
  if (!year || !type) return; // 确保参数有效
  const res = await getChinaOper({
    searchDate: year,
    type: type  // 季度参数
  });
  detail.value = res.data;
};
// watch([date, quarter], ([newDate, newQuarter]) => {
//   fetchData(newDate, newQuarter);
// }, { immediate: true });

// 获取在建机组数据
const fetchList1 = async (year: string, type: string) => {
  if (!year || !type) return;
  const res = await getChinaUnderList({
    searchDate: year,
    type: type
  });
  list1.value = res.data ?? [];
};

// 获取运营机组数据
const fetchList2 = async (year: string, type: string) => {
  if (!year || !type) return;
  const res = await getChinaOperList({
    searchDate: year,
    type: type
  });
  list2.value = res.data ?? [];
};

// 统一获取所有数据
const fetchAllData = (year: string, type: string) => {
  fetchData(year, type);
  fetchList1(year, type);
  fetchList2(year, type);
};

// 监听查询条件变化 - 触发所有数据更新
watch([date, quarter], ([newDate, newQuarter]) => {
  fetchAllData(newDate, newQuarter);
}, { immediate: true });
const chartRef = ref<HTMLDivElement>()
const list3 = ref<any[]>([])
getChinaApproveList().then(res => {
  list3.value = (res.data ?? []).slice(0,5)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
       textStyle: {
                fontSize: 20,       // 字体大小（单位px）[4,8](@ref)
            },
    },
    grid: {
      left: '2%',
      right: '3%',
      top: '8%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: list3.value.map(t => t.year),
      axisLabel: { fontSize: 14 }
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        data: list3.value.map(t => t.unitsCount),
        type: 'bar',
        barWidth: 10,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1, // 垂直渐变（从上到下）
            colorStops: [
              { offset: 0, color: '#0099FC' }, // 顶部颜色
              { offset: 1, color: '#188df0' }  // 底部颜色
            ]
          },
          borderRadius: [10, 10, 0, 0] // 左上 右上 右下 左下 的圆角半径
        },
        sort: { method: 'descending' }
      }
    ]
  };
  setTimeout(() => {
    const myChart = echarts.init(chartRef.value)
    myChart.setOption(option)
  }, 0);

})

const lastUpdateDate = ref('')
getChinaUpdateDeadline().then(res => {
  lastUpdateDate.value = res.msg
})

</script>
<style scoped>
.box {
  transform: scale(0.72);
  transform-origin: 0 0;
}

.bodybg {
  background: url(/images/zyfw_bg1.png) no-repeat center 76px #D9F2FF;
  background-size: cover;
  min-width: 320px;
  /* 改为移动端友好 */
  padding-bottom: 50px;
  overflow: hidden;
}

.menubg {
  background: linear-gradient(180deg, #54a3e2 0%, #3b97e0 100%);
  border-radius: 5px 5px 0px 0px;
  text-align: left;
}

.menubg a {
  color: #fff;
  padding: 5px 20px;
  margin: 4px 10px 0 10px;
  cursor: pointer;
  font-size: 18px;
  display: inline-block;
}

.menubg a:hover {
  color: #c9e7ff;
}

.menubg a.hover {
  background-color: #fff;
  color: #469ee4;
  display: inline-block;
  border-radius: 5px 3px 0 0;
}

.topbg_industry {
  background: url(/images/zyfw_topbg.png) no-repeat top;
  background-size: cover;
  height: 55px;
  width: 100%;
}

.backbg {
  background: url(/images/zyfw_back.png) no-repeat top;
  background-size: contain;
  width: 155px;
  height: 26px;
  color: #fff;
  text-align: center;
  font-size: 16px;
  line-height: 27px;
}

.b_l {
  padding-bottom: 12px;
  border-bottom: 3px solid;
  border-image: linear-gradient(to right, #36b0ff, #0080d4) 1;
  font-weight: 500;
}

.left {
  width: 28.5rem;
}

:deep(.el-table) {
  background: rgba(255, 255, 255, 0.3);
}

:deep(.el-table .cell) {
  color: #303133;
  white-space: normal;
  /* 允许换行 */
  word-break: break-word;
  padding-left:5px;
  /* 长单词换行 */
}

:deep(.el-table .el-table__cell) {}

:deep(.el-table td.el-table__cell) {
  border-bottom: none;
}

:deep(.el-table th.el-table__cell .cell) {
  background-color: #8bd3f7;
  color: #fff;
  padding: 6px 0;
  padding-left: 5px;

  font-weight: normal;
}

:deep(.el-table .cell) {
  color: #212121;
 
  white-space: nowrap;
  word-break: keep-all;
}

:deep(.el-table tr) {
  background-color: rgba(255, 255, 255, 0.5);
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell) {
  background-color: rgba(220, 243, 251, 0.5);
}
:deep(.el-input__inner){font-size: 16px;}
:deep(.el-select__selected-item){font-size: 16px;}
/* 响应式表格列 */

.h-20 {
  height: 20rem;
}

.flex-container {
  display: flex;
  /* flex-wrap: wrap; */
  gap: 1rem;
}

.flex-item {
  flex: 1 1 100%;
  /* 最小320px */
  /* min-width: 400px;  移动端友好 */
}

.main {
    flex: 2 1 540px;
    /* min-width: 640px; 移动端友好 */
}
.bg_dz {
  background: url(/images/zyfw_china_bg.png) no-repeat center 50px;
}

.bg_sz {
  background: url(/images/zyfw_china_digitalbg.png) no-repeat center;
  width: clamp(20px, 5vw, 39px);
  /* 响应式宽度 */
  height: clamp(40px, 8vw, 68px);
  font-size: clamp(16px, 3vw, 34px);

  color: #fff;
  margin: 0 3px 0 3px;
  line-height: 58px;
  text-align: center;
}

.gradient-text {
  font-size: 36px;
  font-weight: bold;
  background: linear-gradient(180deg, #ffe819 6.4%, #ff810e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

@media (max-width: 1024px) {
  .main {
    /* order: 0; 中间内容优先显示 */
    flex-basis: 100%;
  }
}

.zyfw_menubg {
  background: url(/images/zyfw_menubg.png) no-repeat center;
  height: 34px;
  text-align: center;
  color: #fff;

  line-height: 34px;
}

:deep(.demo-form-inline .el-select) {
  --el-select-width: 100px;
}

:deep(.custom-date-picker) {
  width: 120px;
  --el-date-editor-width: 100%;
  /* 覆盖内部变量 */
  height: 42px;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 10px;
  margin-bottom:20px;
}

:deep(.el-select__wrapper) {
  min-height: 42px;
}

.csjb {
  width: 28px;
  height: 28px;
  background: linear-gradient(180deg, #ffe819 6.4%, #ff810e 100%);
  color: #fff;
  text-align: center;
  margin: 0 3px 0 3px;
}

/* 响应式断点 */
@media (max-width: 1024px) {
  .flex-container {
    flex-direction: column;
  }

  .flex-item {
    min-width: 100%;
  }
}

/* 表格响应式 */
@media (max-width: 768px) {
  :deep(.el-table) {
    font-size: 12px;
  }

  :deep(.el-table__cell) {
    padding: 4px 0;
  }
}

:deep(.el-select--large .el-select__wrapper) {
  padding: 6px 16px;
}

:deep(.el-form-item__label) {
  font-size: 16px;
  padding-right: 4px;
  line-height: 42px;
}
</style>