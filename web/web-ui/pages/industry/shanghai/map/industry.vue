<template>
    
    <div class="flex justify-between gap-3 relative mt-[10px] px-3 h-[55rem] box "
        :style="{ transform: isFullscreen ? `scale(1)` : 'scale(0.7)',width: isFullscreen ? `1400px` : '883px' }">
        <div v-if="active != 2" class="legendbg z-10 flex-0">
            <!-- <h2 class="mb-[10px]">
                图例
            </h2> -->
            <div class="leading-[30px]">
                <p class="flex items-center ">
                    <span class="w-[25px] flex items-center justify-center">
                        <img src="/images/legend1.png" class="w-[15px]"></img>
                    </span>
                    <span>规上单位</span>
                </p>
                <p class="flex items-center ">
                    <span class="w-[25px] flex items-center justify-center">
                        <img src="/images/legend3.png" class="w-[15px]"></img>
                    </span>
                    <span>非规上单位</span>
                </p>

            </div>
        </div>
        <div v-show="active != 2" class="flex-item w-700 ml"
            :style="{ 'margin-left': isFullscreen ? `50px !important` : '-50px ', 'width': isFullscreen ? '' : '800px' }">
            <div ref="chartRef" class="h-full w-700"></div>
        </div>

        <div v-show="active == 2" class="flex w-700 justify-center "
            :style="{ 'margin-left': isFullscreen ? `0px` : '0', 'width': isFullscreen ? '' : '750px!important','margin-right': isFullscreen ? `135px` : '33px' }">
          
            <div class="map relative mr" :style="{ 'margin-left': isFullscreen ? `200px` : '0' }">
                <div class="database-point database-point1"></div>
                <div class="database-point database-point2"></div>
                <div class="database-point database-point3"></div>
                <div class="database-point4"></div>
                <div class="database-point5"></div>
                <div class="database-point6"></div>
                <div class="database-point7"></div>
                <div class="absolute allpoint">
                </div>
            </div>
        </div>
        <div class="rightbg h-[48rem] w-[480px] mr-[10px] flex-0" :class="{ 'w-[480px]': !isFullscreen }">
            <div class="exchangecon custom-box">
                <div :class="{ 'menubghover': active == 0 }"
                    @click="active = 0; subTitleActivate = 0; updateMapPoint(Object.values(types1Map).flat())">

                    <div class="menubg">
                        企业分布图
                    </div>
                    <div v-show="active == 0" class="subcon">
                        <span v-for="(item, index) in types1"
                            :class="{ 'hover': subTitleActivate === (item === '全部' ? 0 : 10 + index - 1) }"
                            @click.stop="handleTypes1Click(item, index)">
                            {{ item }}
                        </span>
                    </div>
                </div>
                <div :class="{ 'menubghover': active == 1 }"
                    @click="active = 1; subTitleActivate = 29; updateMapPoint(Object.values(types2Map).flat())">
                    <div class="menubg">
                        创新资源分布图
                    </div>
                    <div v-show="active == 1" class="subcon">
                        <template v-for="(item, index) in types2">
                            <span v-if="item == '全选'" :class="{ 'hover': subTitleActivate == 29 }"
                                @click.stop="subTitleActivate = 29; updateMapPoint(Object.values(types2Map).flat())">{{
                                    item
                                }}</span>
                            <span v-else :class="{ 'hover': subTitleActivate == 20 + index }"
                                @click.stop="subTitleActivate = 20 + index; updateMapPoint(types2Map[item])">{{ item
                                }}</span>
                        </template>
                    </div>
                </div>
                <div :class="{ 'menubghover': active == 2 }" @click="active = 2; subTitleActivate = 0">
                    <div class="menubg">
                        产业基地分布图
                    </div>
                    <div v-show="active == 2" class="subcon">
                        <span :class="{ 'hover': subTitleActivate == 0 }" @click.stop="subTitleActivate = 0">3+4</span>
                        <!-- <span :class="{ 'hover': subTitleActivate == 1 }" @click.stop="subTitleActivate = 1">3+2</span>
                        <span :class="{ 'hover': subTitleActivate == 2 }" @click.stop="subTitleActivate = 2">3+5</span>
                        <span :class="{ 'hover': subTitleActivate == 3 }" @click.stop="subTitleActivate = 3">3+X</span>
                        <span :class="{ 'hover': subTitleActivate == 4 }" @click.stop="subTitleActivate = 4">3+4</span> -->
                    </div>
                    <div v-show="active == 2">
                        <!--4+3-->
                        <table v-show="subTitleActivate == 0" class="table"
                            :style="{ 'font-size': isFullscreen ? `14px` : '17px' }">
                            <tbody>

                                <tr>
                                    <td rowspan="3">
                                        3个创新引领基地
                                    </td>
                                    <td>浦东区域总部及运维评价基地</td>
                                </tr>
                                <tr>
                                    <td>青浦核能科创孵化基地</td>
                                </tr>
                                <tr>
                                    <td>嘉定核技术应用基地</td>
                                </tr>
                                <tr>
                                    <td rowspan="4">
                                        4个产业转型升级基地
                                    </td>
                                    <td>宝山特殊材料制造基地</td>
                                </tr>
                                <tr>
                                    <td>闵行制造服务融合创新示范基地</td>
                                </tr>
                                <tr>
                                    <td>漕河泾核能产业技术创新基地</td>
                                </tr>
                                <tr>
                                    <td>临港重要产业基地</td>
                                </tr>

                            </tbody>
                        </table>

                        <!--3+2-->
                        <!-- <table v-show="subTitleActivate == 1" class="table" :style="{ 'font-size': isFullscreen ? `14px` : '17px' }">
                            <tbody>
                                <tr>
                                    <th colspan="2">
                                        “3+2”产业结构体系
                                    </th>
                                </tr>

                                <tr>
                                    <td>
                                        3
                                    </td>
                                    <td>核电先进制造业、现代服务业与建筑业三大板块</td>
                                </tr>
                                <tr>
                                    <td>
                                        2
                                    </td>
                                    <td>数字化与绿色化“两个转型”</td>
                                </tr>

                            </tbody>
                        </table> -->
                        <!--3+5-->
                        <!-- <table v-show="subTitleActivate == 2" class="table" :style="{ 'font-size': isFullscreen ? `14px` : '17px' }">
                            <tbody>
                                <tr>
                                    <th colspan="2">
                                        “3+5”产业能力体系
                                    </th>
                                </tr>

                                <tr>
                                    <td>
                                        3
                                    </td>
                                    <td>三代堆、四代堆、“核聚变”三个发展层级</td>
                                </tr>
                                <tr>
                                    <td>
                                        5
                                    </td>
                                    <td>核电“五个中心建设”</td>
                                </tr>

                            </tbody>
                        </table> -->
                        <!--3+x-->
                        <!-- <table v-show="subTitleActivate == 3" class="table" :style="{ 'font-size': isFullscreen ? `14px` : '17px' }">
                            <tbody>
                                <tr>
                                    <th colspan="2">
                                        “3+X”产业能力体系
                                    </th>
                                </tr>

                                <tr>
                                    <td>
                                        3
                                    </td>
                                    <td>核能综合利用、核技术应用、核环保等三大领域</td>
                                </tr>
                                <tr>
                                    <td>
                                        X
                                    </td>
                                    <td>其他未来可拓展的产业发展新方向</td>
                                </tr>

                            </tbody>
                        </table> -->
                        <!--3+x-->
                        <!-- <table v-show="subTitleActivate == 4" class="table" :style="{ 'font-size': isFullscreen ? `14px` : '17px' }">
                            <tbody>
                                <tr>
                                    <th colspan="2">
                                        “3+4”支撑条件体系
                                    </th>
                                </tr>

                                <tr>
                                    <td>
                                        3
                                    </td>
                                    <td>新建3个现代化特色产业基地</td>
                                </tr>
                                <tr>
                                    <td>
                                        4
                                    </td>
                                    <td>已布局的4个产业转型升级基地</td>
                                </tr>

                            </tbody>
                        </table> -->

                    </div>
                </div>
            </div>
        </div>
        <el-dialog v-model="fileViewShow" width="800px" append-to-body>
            <template #title>
                <span class="text-[16px]">查看文件</span>
            </template>
            <iframe :src="fileUrl" class="w-full h-[600px]" />
        </el-dialog>
    </div>
   
</template>

<script setup lang="ts">
import { ECOLUMN_CODE } from '~/store/column';
// @ts-ignore
import shanghaiJSON from '/public/maps/上海市.json'
import * as echarts from 'echarts'
import { parse } from 'csv-parse/browser/esm/sync'
import { getDetailByName } from '~/api/company/industryDirectories';
import { getFileListByObjId } from '~/api/file';


const config = useRuntimeConfig()
const parseCsv = (data: any) => {
    return parse(data, {
        columns: true,
        skip_empty_lines: true,
        delimiter: ','
    })
}


const types1 = ['全部', '研发设计', '装备制造', '工程建设', '运行服务', '核能多元利用', '国际组织'];
const types2 = ['全选', '高等院校', '科研院所', '其他创新平台']
interface IData {
    企业名称: string
    企业地址: string
    企业权限: string
    分类: string
    单位分类: string
    地理坐标: string
    统一社会信用代码: string
}

const handleTypes1Click = (item: string, index: number) => {
    if (item === '全部') {
        subTitleActivate.value = 0;
        updateMapPoint(Object.values(types1Map).flat());
    } else {
        subTitleActivate.value = 10 + (index - 1); // 保持原有索引逻辑
        updateMapPoint(types1Map[item]);
    }
};
const types1Map: Record<string, IData[]> = {

    '研发设计': parseCsv(await fetch(config.public.baseUrl + '/csv/研发设计.csv').then(r => r.text())),
    '装备制造': parseCsv(await fetch(config.public.baseUrl + '/csv/装备制造.csv').then(r => r.text())),
    '工程建设': parseCsv(await fetch(config.public.baseUrl + '/csv/工程建设.csv').then(r => r.text())),
    '运行服务': parseCsv(await fetch(config.public.baseUrl + '/csv/运行维护.csv').then(r => r.text())),
    '核能多元利用': parseCsv(await fetch(config.public.baseUrl + '/csv/核能多元利用.csv').then(r => r.text())),
    '国际组织': parseCsv(await fetch(config.public.baseUrl + '/csv/国际组织.csv').then(r => r.text())),
}

const types2Map: Record<string, IData[]> = {
    '高等院校': parseCsv(await fetch(config.public.baseUrl + '/csv/高等院校.csv').then(r => r.text())),
    '科研院所': parseCsv(await fetch(config.public.baseUrl + '/csv/科研院所.csv').then(r => r.text())),
    '其他创新平台': parseCsv(await fetch(config.public.baseUrl + '/csv/创新平台.csv').then(r => r.text())),
}
console.log(types2Map)

definePageMeta({
    layout: 'column',
    name: ECOLUMN_CODE.INDUSTRY_SHANGHAI_MAP_INDUSTRY,
    submenuCode: ECOLUMN_CODE.INDUSTRY_SHANGHAI,
})
// 从字典中获取地图数据
const { nuclear_company_distribute } = useDict('nuclear_company_distribute')


const fileViewShow = ref(false)
const fileUrl = ref('')
const active = ref(0)
const subTitleActivate = ref(-1)
const { isFullscreen, toggleFullscreen } = useFullscreenLayout()

const chartRef = ref<HTMLDivElement>()
const chartIns = ref<echarts.ECharts>()
const option = {
    geo: {
        map: 'shanghai',
        roam: true,
        zoom: 1.2,
        itemStyle: {
            //整体板块的样式
            color: "#7abeef", // 背景
            opacity: 0.5, //透明度
            borderWidth: 1, // 边框宽度
            borderColor: "#1784DE", // 边框颜色
        },
        label: {
            show: true,
            distance: 0.5,
            color: "#000",
            fontSize: 12,
            opacity: 1,


            formatter: function (params: any) {
                return params.name
            },
        },
        shading: "lambert",
        //用于鼠标控制地图旋转等功能
        viewControl: {
            // 用于鼠标的旋转，缩放等视角控制。
            projection: "perspective", // 投影方式，默认为透视投影'perspective'，也支持设置为正交投影'orthographic'。
            autoRotate: false, // 是否开启视角绕物体的自动旋转查看。[ default: false ]
            autoRotateDirection: "cw", // 物体自传的方向。默认是 'cw' 也就是从上往下看是顺时针方向，也可以取 'ccw'，既从上往下看为逆时针方向。
            autoRotateSpeed: 10, // 物体自传的速度。单位为角度 / 秒，默认为10 ，也就是36秒转一圈。
            autoRotateAfterStill: 3, // 在鼠标静止操作后恢复自动旋转的时间间隔。在开启 autoRotate 后有效。[ default: 3 ]
            damping: 0, // 鼠标进行旋转，缩放等操作时的迟滞因子，在大于等于 1 的时候鼠标在停止操作后，视角仍会因为一定的惯性继续运动（旋转和缩放）。[ default: 0.8 ]
            rotateSensitivity: 10, // 旋转操作的灵敏度，值越大越灵敏。支持使用数组分别设置横向和纵向的旋转灵敏度。默认为1, 设置为0后无法旋转。   rotateSensitivity: [1, 0]——只能横向旋转； rotateSensitivity: [0, 1]——只能纵向旋转。
            zoomSensitivity: 10, // 缩放操作的灵敏度，值越大越灵敏。默认为1,设置为0后无法缩放。
            panSensitivity: 10, // 平移操作的灵敏度，值越大越灵敏。默认为1,设置为0后无法平移。支持使用数组分别设置横向和纵向的平移灵敏度
            panMouseButton: "left", // 平移操作使用的鼠标按键，支持：'left' 鼠标左键（默认）;'middle' 鼠标中键 ;'right' 鼠标右键(注意：如果设置为鼠标右键则会阻止默认的右键菜单。)
            rotateMouseButton: "left", // 旋转操作使用的鼠标按键，支持：'left' 鼠标左键;'middle' 鼠标中键（默认）;'right' 鼠标右键(注意：如果设置为鼠标右键则会阻止默认的右键菜单。)

            distance: 100, // [ default: 100 ] 默认视角距离主体的距离，对于 grid3D 和 geo3D 等其它组件来说是距离中心原点的距离,对于 globe 来说是距离地球表面的距离。在 projection 为'perspective'的时候有效。
            minDistance: 40, // [ default: 40 ] 视角通过鼠标控制能拉近到主体的最小距离。在 projection 为'perspective'的时候有效。
            maxDistance: 400, // [ default: 400 ] 视角通过鼠标控制能拉远到主体的最大距离。在 projection 为'perspective'的时候有效。

            alpha: 50, // 视角绕 x 轴，即上下旋转的角度。配合 beta 可以控制视角的方向。[ default: 40 ]
            beta: 15, // 视角绕 y 轴，即左右旋转的角度。[ default: 0 ]
            minAlpha: -720, // 上下旋转的最小 alpha 值。即视角能旋转到达最上面的角度。[ default: 5 ]
            maxAlpha: 720, // 上下旋转的最大 alpha 值。即视角能旋转到达最下面的角度。[ default: 90 ]
            minBeta: -720, // 左右旋转的最小 beta 值。即视角能旋转到达最左的角度。[ default: -80 ]
            maxBeta: 720, // 左右旋转的最大 beta 值。即视角能旋转到达最右的角度。[ default: 80 ]

            center: [-8, -5, 7], // 视角中心点，旋转也会围绕这个中心点旋转，默认为[0,0,0]。左右 上下 前后

            animation: true, // 是否开启动画。[ default: true ]
            animationDurationUpdate: 1000, // 过渡动画的时长。[ default: 1000 ]
            animationEasingUpdate: "cubicInOut", // 过渡动画的缓动效果。[ default: cubicInOut ]
        },
        silent: false,
        regionHeight: 3, //修改整个地图的三维高度
        // boxHeight: 250,
    },
    tooltip: {
        textStyle: {
            fontSize: 20 // 设置字体大小为14px [1,5](@ref)
        },
    },
    legend: {},
    series: []

}

onMounted(() => {
    echarts.registerMap('shanghai', shanghaiJSON)
    chartIns.value = echarts.init(chartRef.value)
    chartIns.value.setOption(option)

    chartIns.value.on('click', (params: any) => {
        if (params.seriesType === 'scatter') {
            getDetailByName(params.name).then(async res => {
                if (res.data?.id) {
                    const file = (await getFileListByObjId({ objId: res.data?.id })).data?.at(0)
                    if (file?.fileUrl) {
                        // fileViewShow.value = true
                        window.open(config.public.backendApi + file.filePath)
                        // fileUrl.value = config.public.backendApi + file.filePath
                    }
                }

            })
        }
    })


    const show = ref(false)
    chartIns.value.on('georoam', (params: any) => {
        console.log(show.value, params.totalZoom)
        if (show.value == false) {
            if (params.totalZoom >= 2) {
                show.value = true
                const option = chartIns.value?.getOption() as echarts.EChartsCoreOption;
                (option?.series as any[]).forEach(item => {
                    item.label.show = true
                    // 因为第一次进来如果滚轮速度过快无法显示标签
                    // 所以先在配置那边把label的show属性设置为true，然后通过label的样式把label隐藏
                    // 这里再通过样式把文字显示出来
                    item.label.color = 'black'
                    item.label.width = 'auto'
                    item.label.overflow = 'none'
                })
                chartIns.value?.setOption(option, true, true)
            }
        }
        if (show.value == true) {
            if (params.totalZoom < 2) {
                show.value = false
                const option = chartIns.value?.getOption() as echarts.EChartsCoreOption;
                (option?.series as any[]).forEach(item => {
                    item.label.show = false
                })
                chartIns.value?.setOption(option, true, true)
            }
        }

    })

    updateMapPoint(Object.values(types1Map).flat());
    subTitleActivate.value = 0;
    //    subTitleActivate.value = 29;

    //  active.value = 0; // 激活创新资源分布图菜单
    //   subTitleActivate.value = 29; // 高亮全选按钮
    //   updateMapPoint(Object.values(types2Map).flat());
})

const updateMapPoint = (list: IData[]) => {
    const series = list.map(t => {
        let symbolSize = 8
        let color = '#1e42a9'
        // if (active.value == 0) {
        if (t.单位分类 == '非规上企业') {
            color = '#1e42a9'
            symbolSize = 8
        } else if (t.单位分类 == '规上企业') {
            color = '#ec2d42'
            symbolSize = 8
        } else {
            color = '#1e42a9'
            symbolSize = 8
        }
        // }

        return {
            type: 'scatter',
            coordinateSystem: 'geo',
            symbol: 'circle',
            symbolSize: symbolSize,
            data: [{ name: t.企业名称, value: [...t.地理坐标.split(/[,，\s]+/), t.企业名称] }],
            zlevel: 1,
            itemStyle: {
                color: color,
                opacity: 1,
            },
            label: {
                show: true, // 显示标签
                formatter: function (params: any) {
                    return params.value[2]; // 返回企业名称
                },
                width: '0px',
                overflow: 'truncate',
                position: 'right', // 标签位置
                color: 'black', // 标签颜色
                fontSize: 16, // 标签字体大小
            },
            tooltip: {
                show: false,
                formatter(params: any) {
                    return `
                                    <div>
                                        <h1>${t.企业名称}</h1>
                                    </div>
                                `
                }
            }
        }
    })
    chartIns.value?.setOption({
        ...option,
        series: series
    }, true)
}

</script>

<style scoped>
.map {
    @apply relative;
    background: url(/images/industry_map.png) no-repeat center;
    background-size: contain;
    width: 700px;
    height: 700px;

    /* width: 48.375rem;
  height: 55rem; */
}

.allpoint {
    @apply left-0 top-0;
    background: url(/images/industrymap_all.png) no-repeat center;
    background-size: contain;
    width: 100%;
    height: 100%;

}


.left {
    width: 28.5rem;
}

.h-20 {
    height: 20rem;
}

.flex-container {
    display: flex;
    /* flex-wrap: wrap; */
    gap: 6rem;
}


.rightbg {
    @apply text-left bg-white/25 backdrop-blur-sm mt-[20px];
    border: 1.5px solid #ffffff;
    border-radius: 10px;
}

.topmenu {
    @apply border-b border-[#97C1D7] mx-[15px] relative flex text-[24px] mt-[2rem] pb-[0.5rem] pl-[1.8rem] text-[#5A5A5A];
}

.topmenu::before {
    @apply absolute w-[15px] h-[5px] left-0 bottom-0 bg-[#0391EF];
    content: '';
}

.topmenu span {
    @apply mx-[1.2rem];
}

.topmenu span:hover {
    @apply text-[#0391EF] cursor-pointer;
}

.topmenu span.hover {
    @apply text-[#0391EF] cursor-pointer;
}

.exchangecon {
    @apply ml-[2rem] text-[1.6rem] leading-[4rem] overflow-y-auto h-[48rem];
}

.gradient-text {
    @apply text-[1.6rem] mr-[5px];
    background: linear-gradient(45deg, #55bbff, #0099ff);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: bold;
}

.exchangecon .tit {
    @apply mt-[1.2rem] mb-[0rem];
}

.exchangecon .pink {
    @apply text-[#FD525B] relative pl-[2.4rem] cursor-pointer;
}

.exchangecon .pink::before {
    @apply absolute w-[36px] h-[36px] left-0 top-[0.9rem];
    content: '';
    background: url(/images/pinkpoint.png) no-repeat;
}

.exchangecon .yellow {
    @apply text-[#FE8616] relative pl-[2.4rem] cursor-pointer;
}

.exchangecon .yellow::before {
    @apply absolute w-[36px] h-[36px] left-0 top-[0.9rem];
    content: '';
    background: url(/images/yellowpoint.png) no-repeat;
}

.detailtit {
    @apply w-[229px] h-[47px] text-[#0391EF] relative pl-[30px] leading-[47px] mt-[5px] text-[1.5rem];
    background: url(/images/industrymap_bg.png) no-repeat;
}

.detailtit::before {
    @apply absolute w-[6px] h-[20px] rounded-[5px] left-[15px] top-[13px];
    background: linear-gradient(153.4deg, #6ac1fa 16.68%, #2ba0ed 83.32%);
    content: '';
}

.custom-box::-webkit-scrollbar {
    width: 8px;
    /* 垂直滚动条宽度 */
    height: 8px;
    /* 水平滚动条高度 */
}

.custom-box::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.5);
    /* 轨道颜色 */
    border-radius: 10px;
    /* 圆角 */
}

.custom-box::-webkit-scrollbar-thumb {
    background: #5c86a1;
    /* 滑块颜色 */
    border-radius: 10px;
    /* 圆角 */
    border: 2px solid #f1f1f1;
    /* 滑块边框 */
}

.custom-box::-webkit-scrollbar-thumb:hover {
    background: #4b87ac;
    /* 鼠标悬停时滑块颜色 */
}

.menubg {
    @apply text-[#0391EF] relative pl-[3.5rem] cursor-pointer text-[1.3rem];
    background: url(/images/bluebg.png) no-repeat;
    background-size: contain;
    width: 440px;
    height: 61px;
    margin-top: 40px;
}

.menubg::before {
    @apply absolute w-[36px] h-[36px] left-[20px] top-[0.9rem];
    content: '';
    background: url(/images/bluepoint.png) no-repeat;
}

.subcon {
    @apply flex flex-wrap mt-[10px];
}

.subcon span {
    @apply text-[#FC8B00] border border-[#FC8B00] px-[15px] mx-[10px] my-[5px] text-[1.2rem] py-[4px] h-[2.5rem] leading-[2.18rem];
}

.subcon span:hover {
    @apply bg-[#f6902c] text-white transition cursor-pointer;
}

.subcon span.hover {
    @apply bg-[#f6902c] text-white transition cursor-pointer;
}

.menubghover .menubg {
    @apply text-[#FC8B00] cursor-pointer;
    background: url(/images/yellowbg.png) no-repeat;
}

.menubghover .menubg:hover {
    @apply text-[#de7c03];
}

.menubghover .menubg::before {
    content: '';
    background: url(/images/yellowpoint.png) no-repeat;
}

.menubg:hover {
    @apply text-[#007dd0];
}

.legendbg {
    @apply absolute;
    /* background: rgba(255, 255, 255, 0.25);
    border: 1.5px solid #ffffff; */
    border-radius: 3px;
    padding: 15px 20px;
    top: 10px;
    left: 10px;
}

.flex-item {
    flex: 0 0;
    /* 最小320px */
    /* min-width: 900px; */
    /* 移动端友好*/
}

.table {
    @apply border border-[#099DDB] leading-[30px] mt-[20px] text-sm w-[427px];
}

.table td {
    @apply border border-[#099DDB] px-[1.5rem] text-[#3C3C3C];
}

.table th {
    @apply text-center bg-[#B1E0FF];
}

.database-point {
    position: absolute;
    color: #fff;
    z-index: 999;
}

.database-point1 {
    left: 390px;
    top: 325px
}

.database-point2 {
    left: 492px;
    top: 446px
}

.database-point3 {
    left: 375px;
    top: 468px
}

.database-point4 {
    left: 375px;
    top: 274px;
    position: absolute;
    color: #fff;
}

.database-point5 {
    left: 446px;
    top: 596px;
    position: absolute;
    color: #fff;
    z-index: 999;
}

.database-point6 {
    left: 347px;
    top: 460px;
    position: absolute;
    color: #fff;
}

.database-point7 {
    left: 266px;
    top: 370px;
    position: absolute;
    color: #fff;
}

.database-point:after,
.database-point:before {
    content: "";
    display: block;
    position: absolute;
    -webkit-border-radius: 50%;
    border-radius: 50%;
    left: -97px;
    top: -74px
}

.database-point:before {
    width: 1px;
    height: 1px;
    background: #ff9534
}

.database-point5:before,
.database-point5:after,
.database-point7:before,
.database-point7::after,
.database-point6:before,
.database-point6::after,
.database-point4:before,
.database-point4::after {
    width: 1px;
    height: 1px;
    background: #fe535c;
    content: "";
    display: block;
    position: absolute;
    -webkit-border-radius: 50%;
    border-radius: 50%;
    left: -12px;
    top: 2px
}

.database-point5:after {
    width: 14px;
    height: 14px;
    left: 35px;
    top: -63px;
    background: transparent;
    -webkit-box-shadow: 0 0 5px #286bbd;
    box-shadow: 0 0 5px #286bbd;
    -webkit-transform-origin: center center;
    -moz-transform-origin: center center;
    -ms-transform-origin: center center;
    -o-transform-origin: center center;
    transform-origin: center center;
    -webkit-animation: g ease-in-out 1.5s infinite;
    -moz-animation: g ease-in-out 1.5s infinite;
    -o-animation: g ease-in-out 1.5s infinite;
    animation: g ease-in-out 1.5s infinite
}

.database-point7::after {
    width: 14px;
    height: 14px;
    left: 87px;
    top: 30px;
    background: transparent;
    -webkit-box-shadow: 0 0 5px #286bbd;
    box-shadow: 0 0 5px #286bbd;
    -webkit-transform-origin: center center;
    -moz-transform-origin: center center;
    -ms-transform-origin: center center;
    -o-transform-origin: center center;
    transform-origin: center center;
    -webkit-animation: g ease-in-out 1.5s infinite;
    -moz-animation: g ease-in-out 1.5s infinite;
    -o-animation: g ease-in-out 1.5s infinite;
    animation: g ease-in-out 1.5s infinite
}

.database-point6::after {
    width: 14px;
    height: 14px;
    left: -10px;
    top: 4px;
    background: transparent;
    -webkit-box-shadow: 0 0 5px #286bbd;
    box-shadow: 0 0 5px #286bbd;
    -webkit-transform-origin: center center;
    -moz-transform-origin: center center;
    -ms-transform-origin: center center;
    -o-transform-origin: center center;
    transform-origin: center center;
    -webkit-animation: g ease-in-out 1.5s infinite;
    -moz-animation: g ease-in-out 1.5s infinite;
    -o-animation: g ease-in-out 1.5s infinite;
    animation: g ease-in-out 1.5s infinite
}

.database-point4::after {
    width: 14px;
    height: 14px;
    left: -10px;
    top: 4px;
    background: transparent;
    -webkit-box-shadow: 0 0 5px #286bbd;
    box-shadow: 0 0 5px #286bbd;
    -webkit-transform-origin: center center;
    -moz-transform-origin: center center;
    -ms-transform-origin: center center;
    -o-transform-origin: center center;
    transform-origin: center center;
    -webkit-animation: g ease-in-out 1.5s infinite;
    -moz-animation: g ease-in-out 1.5s infinite;
    -o-animation: g ease-in-out 1.5s infinite;
    animation: g ease-in-out 1.5s infinite
}

.database-point:after {
    width: 14px;
    height: 14px;
    left: -97px;
    top: -74px;
    background: transparent;
    -webkit-box-shadow: 0 0 5px #ec3a3a;
    box-shadow: 0 0 5px #ec3a3a;
    -webkit-transform-origin: center center;
    -moz-transform-origin: center center;
    -ms-transform-origin: center center;
    -o-transform-origin: center center;
    transform-origin: center center;
    -webkit-animation: g ease-in-out 1.5s infinite;
    -moz-animation: g ease-in-out 1.5s infinite;
    -o-animation: g ease-in-out 1.5s infinite;
    animation: g ease-in-out 1.5s infinite
}

@-webkit-keyframes g {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    50% {
        -webkit-transform: scale(1.5);
        transform: scale(1.5)
    }

    to {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@-moz-keyframes g {
    0% {
        -moz-transform: scale(1);
        transform: scale(1)
    }

    50% {
        -moz-transform: scale(1.5);
        transform: scale(1.5)
    }

    to {
        -moz-transform: scale(1);
        transform: scale(1)
    }
}

@-o-keyframes g {
    0% {
        -o-transform: scale(1);
        transform: scale(1)
    }

    50% {
        -o-transform: scale(1.5);
        transform: scale(1.5)
    }

    to {
        -o-transform: scale(1);
        transform: scale(1)
    }
}

@keyframes g {
    0% {
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -o-transform: scale(1);
        transform: scale(1)
    }

    50% {
        -webkit-transform: scale(1.5);
        -moz-transform: scale(1.5);
        -o-transform: scale(1.5);
        transform: scale(1.5)
    }

    to {
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -o-transform: scale(1);
        transform: scale(1)
    }
}

@media screen and (min-width: 1366px) and (max-width: 1920px) {
    .w-700 {
        width: 1000px;
        box-sizing: border-box;
    }
    .mapr{margin-right:135px;}
    /* .map {
        margin-right: 120px 
    } */

}

@media (max-width: 1366px) {
    .w-700 {
        width: 700px !important;
    }

    .ml {
        margin-left: 20px !important
    }

    .map {
        margin-left: -120px !important
    }

    .database-point2 {
        left: 492px;
        top: 446px
    }

    .database-point3 {
        left: 375px;
        top: 468px
    }

    .database-point4 {
        left: 375px;
        top: 274px;
        position: absolute;
        color: #fff;
    }

    .database-point5 {
        left: 446px;
        top: 596px;
        position: absolute;
        color: #fff;
        z-index: 999;
    }

    .database-point6 {
        left: 347px;
        top: 460px;
        position: absolute;
        color: #fff;
    }

    .database-point7 {
        left: 266px;
        top: 370px;
        position: absolute;
        color: #fff;
    }

    .database-point:after,
    .database-point:before {
        content: "";
        display: block;
        position: absolute;
        -webkit-border-radius: 50%;
        border-radius: 50%;
        left: -97px;
        top: -74px
    }

    .database-point:before {
        width: 1px;
        height: 1px;
        background: #ff9534
    }

    .database-point5:before,
    .database-point5:after,
    .database-point7:before,
    .database-point7::after,
    .database-point6:before,
    .database-point6::after,
    .database-point4:before,
    .database-point4::after {
        width: 1px;
        height: 1px;
        background: #fe535c;
        content: "";
        display: block;
        position: absolute;
        -webkit-border-radius: 50%;
        border-radius: 50%;
        left: -12px;
        top: 2px
    }

    .database-point5:after {
        width: 14px;
        height: 14px;
        left: 35px;
        top: -63px;
        background: transparent;
        -webkit-box-shadow: 0 0 5px #286bbd;
        box-shadow: 0 0 5px #286bbd;
        -webkit-transform-origin: center center;
        -moz-transform-origin: center center;
        -ms-transform-origin: center center;
        -o-transform-origin: center center;
        transform-origin: center center;
        -webkit-animation: g ease-in-out 1.5s infinite;
        -moz-animation: g ease-in-out 1.5s infinite;
        -o-animation: g ease-in-out 1.5s infinite;
        animation: g ease-in-out 1.5s infinite
    }

    .database-point7::after {
        width: 14px;
        height: 14px;
        left: 87px;
        top: 30px;
        background: transparent;
        -webkit-box-shadow: 0 0 5px #286bbd;
        box-shadow: 0 0 5px #286bbd;
        -webkit-transform-origin: center center;
        -moz-transform-origin: center center;
        -ms-transform-origin: center center;
        -o-transform-origin: center center;
        transform-origin: center center;
        -webkit-animation: g ease-in-out 1.5s infinite;
        -moz-animation: g ease-in-out 1.5s infinite;
        -o-animation: g ease-in-out 1.5s infinite;
        animation: g ease-in-out 1.5s infinite
    }

    .database-point6::after {
        width: 14px;
        height: 14px;
        left: -10px;
        top: 4px;
        background: transparent;
        -webkit-box-shadow: 0 0 5px #286bbd;
        box-shadow: 0 0 5px #286bbd;
        -webkit-transform-origin: center center;
        -moz-transform-origin: center center;
        -ms-transform-origin: center center;
        -o-transform-origin: center center;
        transform-origin: center center;
        -webkit-animation: g ease-in-out 1.5s infinite;
        -moz-animation: g ease-in-out 1.5s infinite;
        -o-animation: g ease-in-out 1.5s infinite;
        animation: g ease-in-out 1.5s infinite
    }

    .database-point4::after {
        width: 14px;
        height: 14px;
        left: -10px;
        top: 4px;
        background: transparent;
        -webkit-box-shadow: 0 0 5px #286bbd;
        box-shadow: 0 0 5px #286bbd;
        -webkit-transform-origin: center center;
        -moz-transform-origin: center center;
        -ms-transform-origin: center center;
        -o-transform-origin: center center;
        transform-origin: center center;
        -webkit-animation: g ease-in-out 1.5s infinite;
        -moz-animation: g ease-in-out 1.5s infinite;
        -o-animation: g ease-in-out 1.5s infinite;
        animation: g ease-in-out 1.5s infinite
    }

    .database-point:after {
        width: 14px;
        height: 14px;
        left: -97px;
        top: -74px;
        background: transparent;
        -webkit-box-shadow: 0 0 5px #ec3a3a;
        box-shadow: 0 0 5px #ec3a3a;
        -webkit-transform-origin: center center;
        -moz-transform-origin: center center;
        -ms-transform-origin: center center;
        -o-transform-origin: center center;
        transform-origin: center center;
        -webkit-animation: g ease-in-out 1.5s infinite;
        -moz-animation: g ease-in-out 1.5s infinite;
        -o-animation: g ease-in-out 1.5s infinite;
        animation: g ease-in-out 1.5s infinite
    }

    @-webkit-keyframes g {
        0% {
            -webkit-transform: scale(1);
            transform: scale(1)
        }

        50% {
            -webkit-transform: scale(1.5);
            transform: scale(1.5)
        }

        to {
            -webkit-transform: scale(1);
            transform: scale(1)
        }
    }

    @-moz-keyframes g {
        0% {
            -moz-transform: scale(1);
            transform: scale(1)
        }

        50% {
            -moz-transform: scale(1.5);
            transform: scale(1.5)
        }

        to {
            -moz-transform: scale(1);
            transform: scale(1)
        }
    }

    @-o-keyframes g {
        0% {
            -o-transform: scale(1);
            transform: scale(1)
        }

        50% {
            -o-transform: scale(1.5);
            transform: scale(1.5)
        }

        to {
            -o-transform: scale(1);
            transform: scale(1)
        }
    }

    @keyframes g {
        0% {
            -webkit-transform: scale(1);
            -moz-transform: scale(1);
            -o-transform: scale(1);
            transform: scale(1)
        }

        50% {
            -webkit-transform: scale(1.5);
            -moz-transform: scale(1.5);
            -o-transform: scale(1.5);
            transform: scale(1.5)
        }

        to {
            -webkit-transform: scale(1);
            -moz-transform: scale(1);
            -o-transform: scale(1);
            transform: scale(1)
        }
    }

    @media screen and (min-width: 1366px) and (max-width: 1920px) {
        .w-700 {
            width: 1000px
        }

        .ml0 {
            margin-left: 20px !important
        }

    }

    @media (max-width: 1366px) {
        .w-700 {
            width: 700px !important;
        }

        .ml0 {
            margin-left: 420px !important
        }

    }

    @media screen and (min-width: 1366px) and (max-width: 1920px) {

        .map {
            @apply relative;
            background: url(/images/industry_map.png) no-repeat center;
            background-size: contain;
            width: 1000px;
            height: 700px;
            margin-top: 60px
                /* width: 48.375rem;
  height: 55rem; */
        }



        .database-point1 {
            left: 539px;
            top: 322px
        }

        .database-point2 {
            left: 644px;
            top: 446px
        }

        .database-point3 {
            left: 523px;
            top: 469px
        }

        .database-point4 {
            left: 525px;
            top: 273px;
            position: absolute;
            color: #fff;
        }

        .database-point5 {
            left: 601px;
            top: 600px;
            position: absolute;
            color: #fff;
            z-index: 999;
        }

        .database-point6 {
            left: 496px;
            top: 463px;
            position: absolute;
            color: #fff;
        }

        .database-point7 {
            left: 418px;
            top: 372px;
            position: absolute;
            color: #fff;
        }

        .database-point:after,
        .database-point:before {
            content: "";
            display: block;
            position: absolute;
            -webkit-border-radius: 50%;
            border-radius: 50%;
            left: -97px;
            top: -74px
        }

        .database-point:before {
            width: 1px;
            height: 1px;
            background: #ff9534
        }

        .database-point5:before,
        .database-point5:after,
        .database-point7:before,
        .database-point7::after,
        .database-point6:before,
        .database-point6::after,
        .database-point4:before,
        .database-point4::after {
            width: 1px;
            height: 1px;
            background: #fe535c;
            content: "";
            display: block;
            position: absolute;
            -webkit-border-radius: 50%;
            border-radius: 50%;
            left: -12px;
            top: 2px
        }

        .database-point5:after {
            width: 14px;
            height: 14px;
            left: 35px;
            top: -63px;
            background: transparent;
            -webkit-box-shadow: 0 0 5px #286bbd;
            box-shadow: 0 0 5px #286bbd;
            -webkit-transform-origin: center center;
            -moz-transform-origin: center center;
            -ms-transform-origin: center center;
            -o-transform-origin: center center;
            transform-origin: center center;
            -webkit-animation: g ease-in-out 1.5s infinite;
            -moz-animation: g ease-in-out 1.5s infinite;
            -o-animation: g ease-in-out 1.5s infinite;
            animation: g ease-in-out 1.5s infinite
        }

        .database-point7::after {
            width: 14px;
            height: 14px;
            left: 87px;
            top: 30px;
            background: transparent;
            -webkit-box-shadow: 0 0 5px #286bbd;
            box-shadow: 0 0 5px #286bbd;
            -webkit-transform-origin: center center;
            -moz-transform-origin: center center;
            -ms-transform-origin: center center;
            -o-transform-origin: center center;
            transform-origin: center center;
            -webkit-animation: g ease-in-out 1.5s infinite;
            -moz-animation: g ease-in-out 1.5s infinite;
            -o-animation: g ease-in-out 1.5s infinite;
            animation: g ease-in-out 1.5s infinite
        }

        .database-point6::after {
            width: 14px;
            height: 14px;
            left: -10px;
            top: 4px;
            background: transparent;
            -webkit-box-shadow: 0 0 5px #286bbd;
            box-shadow: 0 0 5px #286bbd;
            -webkit-transform-origin: center center;
            -moz-transform-origin: center center;
            -ms-transform-origin: center center;
            -o-transform-origin: center center;
            transform-origin: center center;
            -webkit-animation: g ease-in-out 1.5s infinite;
            -moz-animation: g ease-in-out 1.5s infinite;
            -o-animation: g ease-in-out 1.5s infinite;
            animation: g ease-in-out 1.5s infinite
        }

        .database-point4::after {
            width: 14px;
            height: 14px;
            left: -10px;
            top: 4px;
            background: transparent;
            -webkit-box-shadow: 0 0 5px #286bbd;
            box-shadow: 0 0 5px #286bbd;
            -webkit-transform-origin: center center;
            -moz-transform-origin: center center;
            -ms-transform-origin: center center;
            -o-transform-origin: center center;
            transform-origin: center center;
            -webkit-animation: g ease-in-out 1.5s infinite;
            -moz-animation: g ease-in-out 1.5s infinite;
            -o-animation: g ease-in-out 1.5s infinite;
            animation: g ease-in-out 1.5s infinite
        }

        .database-point:after {
            width: 14px;
            height: 14px;
            left: -97px;
            top: -74px;
            background: transparent;
            -webkit-box-shadow: 0 0 5px #ec3a3a;
            box-shadow: 0 0 5px #ec3a3a;
            -webkit-transform-origin: center center;
            -moz-transform-origin: center center;
            -ms-transform-origin: center center;
            -o-transform-origin: center center;
            transform-origin: center center;
            -webkit-animation: g ease-in-out 1.5s infinite;
            -moz-animation: g ease-in-out 1.5s infinite;
            -o-animation: g ease-in-out 1.5s infinite;
            animation: g ease-in-out 1.5s infinite
        }
    }
}
</style>
