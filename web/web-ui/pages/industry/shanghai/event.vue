<template>
  <div class="mx-[11px] overflow-hidden mb-[200px] min-h-[700px]  relative">
    <div class="dsj overflow-hidden">
      <div class="pl-[40px] pt-[64px] flex ">
        <img src="/images/cydsj_text.png" class="w-[260px]">
        <span
          class='absolute block-inline px-[10px] py-[5px] bg-[#018ade]/70 right-[25px] top-[115px] text-white text-sm rounded-[5px] cursor-pointer hover:text-[#b9e7fe]'
          @click="dialogVisible = true">
          <em class='iconfont icon-caidan' />查看附件
        </span>
      </div>
      <div class="mx-[30px] mr-[25px] mt-[30px] h-[110px] flex overflow-hidden bg-[#318AD9]/50 ">
        <div :class="{ 'hover': active == 1970 }" class="shuzi relative" @click="setActive(1970)">
          <p>1970</p>
          <span>～</span>
          <p class="two">
            1979
          </p>
        </div>
        <div :class="{ 'hover': active == 1980 }" class="shuzi relative" @click="setActive(1980)">
          <p>1980</p>
          <span>～</span>
          <p class="two">
            1989
          </p>
        </div>
        <div :class="{ 'hover': active == 1990 }" class="shuzi relative" @click="setActive(1990)">
          <p>1990</p>
          <span>～</span>
          <p class="two">
            1999
          </p>
        </div>
        <div :class="{ 'hover': active == 2000 }" class="shuzi relative" @click="setActive(2000)">
          <p>2000</p>
          <span>～</span>
          <p class="two">
            2009
          </p>
        </div>
        <div :class="{ 'hover': active == 2010 }" class="shuzi relative" @click="setActive(2010)">
          <p>2010</p>
          <span>～</span>
          <p class="two">
            2019
          </p>
        </div>
        <div :class="{ 'hover': active == 2020 }" class="shuzi relative" @click="setActive(2020)">
          <p>2020</p>
          <span>～</span>
          <p class="two font-bold">
            至今
          </p>
        </div>
      </div>
      <div
        class="arial_font dsj_white mx-[30px] mr-[25px] h-[78px] flex bg-white/50 px-[20px] text-[22px] text-[#0A69BD] leading-[78px] backdrop-blur-[2px]">
        <template v-for="(item, index) in 10">
          <div v-if="dayjs().year() >= active + index && yearStatusMap.get(active + index)" :class="{ 'hover': currentYear == active + index }"
            @click="currentYear = active + index">
            {{ active + index }}
          </div>
        </template>

      </div>
      
      <div class="mx-[30px] mr-[25px] border-b border-[#EBEBEB] py-[20px] text-center">
        <h1 class="text-[32px]">
          {{ currentYear }}年大事记
        </h1>
      </div>
      <div class="relative mx-[30px] mr-[25px] mt-[24px]">
        <span class="iconfont icon-tianchongxing- absolute left-[23px] top-[-15px] text-[#B5C7D6]" />
        <div v-for="item in list" class="flex">
          <div class="flex-0 ml-[30px] w-[22px] border-l border-[#EBEBEB]">
            <span class="point ml-[-20px] mt-[20px] inline-block" />
          </div>
          <div class="ml-[10px] flex-1 text-left">
            <div class="mt-[15px] text-[20px] text-[#333] font-bold">
              {{ item.date }}
            </div>
            <div class="mb-[30px] mt-[10px]">
              {{ item.describes }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <el-dialog v-model="dialogVisible" width="800" class="custom-dialog" :show-close="false">
    <span
      class="iconfont icon-guanbi2 absolute right-[20px] top-[20px] cursor-pointer text-[24px] text-[#717070] hover:text-[#027cd1]"
      @click="dialogVisible = false" />
    <div class="pt-[10px] text-[24px] text-[#000]">
      大事记附件列表
    </div>
    <div
      class="my-[20px] border-b border-t border-[#DEDEDE] py-[15px] text-sm text-[#717070] leading-[30px] text-left pl-[20px] h-[350px] overflow-y-auto">
      <p class="grid grid-cols-[90%_1fr]" v-for="item in fileList">
        <a class="cursor-pointer" :href="config.public.backendApi + item.filePath"
          download>{{ item.fileName }}</a>
          <el-button type="primary" link @click="handlePreview(item)">预览</el-button>
      </p>
    </div>

  </el-dialog>

</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import { range } from 'lodash';
import { getBigEventYearStatus, getContentMemorabiliaInfoList, getMemorabiliaFile } from '~/api/release';
import { ECOLUMN_CODE } from '~/store/column';

definePageMeta({
  name: ECOLUMN_CODE.INDUSTRY_SHANGHAI_EVENT,
  layout: 'column',

})

const config = useRuntimeConfig()
interface IEvent {
  id?: string
  date?: string
  describes?: string
}
const dialogVisible = ref(false)
const year = useRoute().query.year as string
const active = ref(year ? parseInt(`${year.substring(0, 3)}0`) : 1970)
const currentYear = ref(year ? parseInt(year) : active.value)
const setActive = (year: number) => {
  active.value = year
  currentYear.value = year
}
const columnId = getColumnIdByCode(ECOLUMN_CODE.INDUSTRY_SHANGHAI_EVENT)
const list = ref<IEvent[]>([])
watch(currentYear, () => {
  getContentMemorabiliaInfoList({ columnId: columnId, articleTime: currentYear.value }, { pageNum: 1, pageSize: 10000 }).then(res => {
    list.value = res.data?.map(item => {
      return {
        id: item.id,
        date: item.dates,
        describes: item.content
      }
    }) ?? []
  })
}, { immediate: true })

const getYearList = computed(() => {
    return range(0, 10).map(t => active.value + t)
})
const yearStatusMap = ref<Map<number, boolean>>(new Map())
watch(getYearList, async () => {
    const res = await getBigEventYearStatus(columnId!, getYearList.value)
    yearStatusMap.value.clear()
    res.data?.forEach(item => {
        yearStatusMap.value.set(parseInt(item.year), item.result)
    })
}, {immediate: true})

const fileList = ref<any[]>([])
getMemorabiliaFile({ columnId: columnId }).then(res => {
  fileList.value = res.data ?? []
})
const handlePreview = (file: any) => {
    window.open(config.public.backendApi + file.filePath)
}
</script>

<style scoped>
.dsj {
  background: url(/images/banner_dsj.png) no-repeat top rgba(255, 255, 255, 0.5);
  background-size: 100%;
  min-height: 490px;
  overflow: hidden;
}

.dsj .shuzi {
  width: 135px;
  margin-top: 10px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  position: relative;
  z-index: 0;
}

.dsj .shuzi::after {
  content: '';
  position: absolute;
  background: url(/images/dsj_jiantou.png) no-repeat center;
  width: 86px;
  height: 86px;
  top: 10px;
  right: -45px;
  z-index: -1;
}

.dsj .shuzi p {
  font-family: 'impact';
  font-size: 26px;
  margin: 4px 0;
}

.dsj .shuzi p:hover {
  background-image: -webkit-linear-gradient(top, #ffc635, #fff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-family: 'impact';
  font-size: 26px;
  margin: 4px 0;
}

.dsj .shuzi p.two {
  padding-left: 70px;
}

.dsj .shuzi span {
  position: absolute;
  font-size: 22px;
  top: 25px;
  right: 36px;

  font-family: 'impact';
  font-weight: bold;
}

.dsj .shuzi:hover p {
  background-image: -webkit-linear-gradient(top, #ffc635, #fff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.dsj .shuzi:hover span {
  background-image: -webkit-linear-gradient(top, #ffc635, #fff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.dsj .shuzi.hover p {
  background-image: -webkit-linear-gradient(top, #ffc635, #fff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.dsj .shuzi.hover span {
  background-image: -webkit-linear-gradient(top, #ffc635, #fff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.dsj_white div {
  width: 10%;
  cursor: pointer;
  position: relative;
}

.dsj_white div:hover {
  background-color: #ffa631;
  color: #fff;
}

.dsj_white div.hover {
  background-color: #ffa631;
  color: #fff;
}

.dsj_white div.hover::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid #ffa631;
  transition: opacity 0.3s ease;
}

.point {
  width: 15px;
  height: 15px;
  background-color: #fd7216;
  border: solid 3px #fff1dd;
  border-radius: 50%;
}
</style>
<style>
.custom-dialog {
  background: url(/images/kepu-digital-bg.png) #fff;
}

img {
  display: inline-block !important;
}
</style>