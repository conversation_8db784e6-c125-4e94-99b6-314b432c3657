<template>
  <div :class="{ 'px-[20px] py-[20px]': !isFullscreen }" class="mb-[100px]">
    <div class="bodybg" :style="{ 'min-height': isFullscreen ? `750px` : '550px' }">
      <div class="topbg_industry relative mx-auto w-full flex justify-center">
        <h1 class="ys_font mt-[2px] text-[30px] text-white tracking-[10px]">
          世界核电
        </h1>

        <div class="backbg absolute right-[150px] top-[10px] mr-[15px] cursor-pointer">
          <a class="text-[#fff] hover:color-[#318AD9] cursor-pointer" @click="toggleFullscreen()">
            <span v-if="!isFullscreen">全屏</span>
            <span v-else>退出全屏</span>
          </a>
        </div>
        <div class="backbg absolute right-[30px] top-[10px] cursor-pointer">
          <a class="text-[#fff] hover:color-[#318AD9]" @click="$router.back()">返回</a>
        </div>


      </div>
      <div class="flex-container mt-[10px] px-[15px] box pb-[50px] mx-[100px]"
        :style="{ transform: isFullscreen ? `scale(1)` : 'scale(1)' }">
        <div class="flex-item  mb-[20px]">
          <h1 class="ys_font mt-[10px] text-[22px] text-[#4F5D69] font-500 text-center ">
            年度主要变化情况
          </h1>
          <el-form :inline="true"
            class="flex justify-center demo-form-inline mt-[10px] border border-[#b6e3f9] bg-[rgba(217,242,255,0.4)] pt-[5px]">
            <el-form-item label="统计年份">
              <el-date-picker v-model="searchForm.searchDate" type="year" value-format="YYYY" placeholder="选择年度"
                clearable class="custom-date-picker" :disabled-date="(date: Date) => dayjs(date).isAfter(dayjs(), 'year')" />
            </el-form-item>
  
            <el-form-item>
              <el-button type="primary" :icon="Search" class="custom-height-btn" @click="search">
                查询
              </el-button>
            </el-form-item>
          </el-form>
          <el-table 
            :data="tableList" 
            :span-method="handleSpanMethod" 
            ref="tableRef"  
            border 
            :cell-class-name="getCellClassName"
            :row-class-name="getRowClassName"
            :row-style="handleRowStyle"
            
            :style="{ 'font-size': isFullscreen ? `15px` : '15px' }"
          > 
            <el-table-column type="index" width="70" label="序号" />
            <el-table-column 
              prop="status" 
              label="变化情况" 
              width="120" 
              show-overflow-tooltip 
              :tooltip-content="getStatusTooltipContent"
            />
            <el-table-column 
              prop="jzmc" 
              label="机组名称" 
               
              show-overflow-tooltip 
              :tooltip-content="getJzmcTooltipContent"
            />
            <el-table-column prop="crewInformation" label="机组信息" show-overflow-tooltip  />
            <el-table-column prop="years" label="时间" width="120">
              <template #default="scope">
              {{ scope.row.years }}年
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="flex-item">
          <div class="overflow-hidden bg-[rgba(255,255,255,0.5)] pb-[40px]">
            <h2 class="bg_menu mt-[5px] flex justify-between">
              <div class="flex justify-center">
                <span class="ml-[5px] mt-[5px]">年度主要变化情况</span>
              </div>
            </h2>
            <div ref="chartRef" class="mt-[20px] flex justify-center p-[10px] pt-[20px]" style="height: 300px">

            </div>

          </div>
        </div>
      </div>

    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  Search,
} from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { groupBy } from 'lodash'
import {  ref, nextTick } from 'vue'
import { reactive } from 'vue'
import { getYearChangeList, type IYearChangeList } from '@/api/crawler/global'
import * as echarts from 'echarts'


// 动态生成的状态映射和排序
const EHd_Year_change_map = ref<Record<string, string>>({})
const STATUS_ORDER_MAP = ref<Record<string, number>>({})

const { isFullscreen, toggleFullscreen } = useFullscreenLayout()
const tableRef = ref()
const searchForm = reactive({
  searchDate: '',
})
const list = ref<IYearChangeList[]>([])
const statusGroupList = ref<{ status: string, list: { date: string, jzmc: string }[] }[]>([])
const tableList = ref<any[]>([])

// 动态生成状态映射和排序
const generateStatusMappings = (data: IYearChangeList[]) => {
  // 获取所有唯一的状态值
  const uniqueStatuses = [...new Set(data.map(item => item.status.trim()))]
  
  // 生成状态映射
  const statusMap: Record<string, string> = {}
  uniqueStatuses.forEach(status => {
    statusMap[status] = status
  })
  EHd_Year_change_map.value = statusMap
  
  // 生成状态排序映射
  const orderMap: Record<string, number> = {}
  uniqueStatuses.forEach((status, index) => {
    orderMap[status] = index + 1
  })
  STATUS_ORDER_MAP.value = orderMap
  
  console.log('动态生成的状态映射:', statusMap)
  console.log('动态生成的状态排序:', orderMap)
}
interface TableRowData {
  _rowInGroup: number;
  _groupIndex: symbol;
  status: string;
  years: string;
  jzmc:string;
  crewInformation: string;
  rowspan?: number; // 可选属性
  // 其他实际存在的字段...
}
type SpanMethodParams = {
  row: any
  column: any
  rowIndex: number
  columnIndex: number
}


// 计算行背景色 - 每个状态组一种颜色，相邻组交替
const getRowBackgroundColor = (rowIndex: number) => {
  const row = tableList.value[rowIndex];
  if (!row) return '#ffffff';
  
  // 根据状态组索引交替使用两种颜色
  const groupIndex = STATUS_ORDER_MAP.value[row.status] || 6;
  const color = groupIndex % 2 === 0 ? '#edf6ff' : '#ffffff';
  return color;
};

// 行样式计算
const handleRowStyle = ({ rowIndex }: { rowIndex: number }) => {
  const backgroundColor = getRowBackgroundColor(rowIndex);
  return { backgroundColor };
};

// 获取行类名
const getRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  const backgroundColor = getRowBackgroundColor(rowIndex);
  return backgroundColor === '#edf6ff' ? 'even-group-row' : 'odd-group-row';
};

// 获取单元格类名
const getCellClassName = ({ rowIndex, columnIndex }: { rowIndex: number; columnIndex: number }) => {
  const row = tableList.value[rowIndex];
  if (!row) return '';
  
  // 为合并单元格添加特殊类名
  if (columnIndex === 1 && row.rowspan > 1) {
    const backgroundColor = getRowBackgroundColor(rowIndex);
    return backgroundColor === '#edf6ff' ? 'merged-cell-even' : 'merged-cell-odd';
  }
  
  // 为所有单元格添加组类名，确保背景色一致
  const backgroundColor = getRowBackgroundColor(rowIndex);
  return backgroundColor === '#edf6ff' ? 'even-group-cell' : 'odd-group-cell';
};

// 获取变化情况列的悬浮提示内容
const getStatusTooltipContent = (row: any, column: any) => {
  if (column.property === 'status') {
    const rowIndex = tableList.value.indexOf(row);
    if (rowIndex === -1) return row.status;
    
    const currentRow = tableList.value[rowIndex];
    if (currentRow.rowspan > 1) {
      // 这是一个合并单元格，需要显示该组的所有信息
      const groupStartIndex = rowIndex;
      const groupEndIndex = groupStartIndex + currentRow.rowspan;
      const groupRows = tableList.value.slice(groupStartIndex, groupEndIndex);
      
      // 构建提示内容
      let tooltipContent = `变化情况：${currentRow.status}\n`;
      tooltipContent += `包含机组：\n`;
      
      groupRows.forEach((groupRow, index) => {
        tooltipContent += `${index + 1}. ${groupRow.jzmc} - ${groupRow.crewInformation} (${groupRow.years}年)\n`;
      });
      
      tooltipContent += `\n机组数量：${groupRows.length}个`;
      
      return tooltipContent;
    }
  }
  return row[column.property];
};

// 获取机组名称列的悬浮提示内容
const getJzmcTooltipContent = (row: any, column: any) => {
  if (column.property === 'jzmc') {
    const rowIndex = tableList.value.indexOf(row);
    if (rowIndex === -1) return row.jzmc;
    
    const currentRow = tableList.value[rowIndex];
    
    // 构建提示内容
    let tooltipContent = `机组名称：${currentRow.jzmc}\n`;
    tooltipContent += `变化情况：${currentRow.status}\n`;
    tooltipContent += `机组信息：${currentRow.crewInformation}\n`;
    tooltipContent += `时间：${currentRow.years}年`;
    
    // 如果该行属于合并组，显示组信息
    if (currentRow.rowspan > 1) {
      const groupStartIndex = rowIndex;
      const groupEndIndex = groupStartIndex + currentRow.rowspan;
      const groupRows = tableList.value.slice(groupStartIndex, groupEndIndex);
      
      tooltipContent += `\n\n所属组：${currentRow.status}组 (共${groupRows.length}个机组)`;
    }
    
    return tooltipContent;
  }
  return row[column.property];
};

const handleSpanMethod = ({
  row,
  column,
  rowIndex,
  columnIndex
}: SpanMethodParams) => { // 直接使用无需导入
  if (columnIndex === 1) {
    return {
      rowspan: row.rowspan, // 从行数据获取合并行数
      colspan: 1
    };
  }
  return { rowspan: 1, colspan: 1 };
}

// 3. 数据分组与表格数据生成
const generateTableData = (normalizedList: any[]) => {
  const grouped = Object.entries(
    groupBy(normalizedList, 'status')
  ).sort((a, b) => STATUS_ORDER_MAP.value[a[0]] - STATUS_ORDER_MAP.value[b[0]]);
  
  tableList.value = []
  grouped.forEach(([status, items]) => {
    (items as any[]).forEach((item: any, index: number) => {
      const rowspan = index === 0 ? (items as any[]).length : 0;
      console.log(`状态组 "${status}" 第 ${index} 行: rowspan=${rowspan}`); // 调试输出
      tableList.value.push({ ...item, rowspan });
    });
  });
}



const search = async () => {
  try {
    const res = await getYearChangeList(searchForm) as any
    if (!res.rows) return
    
    const normalizedList = (res.rows ?? []).map((item: any) => ({
      ...item,
      status: item.status.trim()
    }))
    
    // 动态生成状态映射
    generateStatusMappings(normalizedList)
    
    generateTableData(normalizedList);
    
    // 强制表格重绘
    nextTick(() => {
      tableRef.value?.doLayout()
    })
    
    chart(normalizedList)
  } catch (error) {
    console.error('数据加载失败:', error)
  }
}
// 初始化时调用搜索，生成状态映射
search()
nextTick(() => {
  tableRef.value?.doLayout(); // 强制表格重绘[2](@ref)
});
const chartRef = ref()
const chart = (data: IYearChangeList[]) => {

  const groupData = groupBy(data, 'status')
  // 渐变生成器
  const generateGradient = (startColor: string, endColor: string, direction: any) => {
    const dirMap: { [key: string]: number[] } = {
      right: [0, 0, 1, 0],   // From left to right
      bottom: [0, 0, 0, 1],  // From top to bottom
      diagonal: [0, 0, 1, 1] // Diagonal
    };

    const gradientDirection = dirMap[direction] || dirMap['right'];

    // @ts-ignore
    return new echarts.graphic.LinearGradient(...gradientDirection as any, [
      { offset: 0, color: startColor },
      { offset: 1, color: endColor }
    ]);
  }

  // 定义渐变配色方案
  const GRADIENTS = [
    { start: '#5175FD', end: '#52AEFF', dir: 'right' },
    { start: '#EB8B00', end: '#ECB906', dir: 'diagonal' },
    { start: '#67D98D', end: '#5ACDD1', dir: 'bottom' },
    { start: '#E83C31', end: '#FF6461', dir: 'right' }
  ]
  const option = {
    // color: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEEAD'], // 颜色数组
    title: {
      text: 'Referer of a Website',
      subtext: 'Fake Data',
      left: 'center',
      show: false
    },
    tooltip: {
            trigger: 'item',
            // 显示名称+数据+百分比
            formatter: '{b}<br/> {c} ({d}%)'  // {a}系列名, {b}数据名, {c}数值, {d}百分比
        },
    series: [
      {
        name: '数量',
        type: 'pie',
        radius: ['30%', '70%'],
        // 先定义状态颜色映射


        data: Object.keys(groupData).map((t, i) => {
          return {
            name: EHd_Year_change_map.value[t] ?? (t),
            value: groupData[t].length,
            itemStyle: {
              color: generateGradient(
                GRADIENTS[i % 4].start,
                GRADIENTS[i % 4].end,
                GRADIENTS[i % 4].dir
              )
            }
          }
        }),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          formatter: function(params:any) {
        return `${params.name}${(params.percent ?? 0).toFixed(1)}%`;
      },// 添加百分比显示
          textBorderColor: '#000', // 文字描边颜色
          textBorderWidth: 0,
          color: '#222',
          fontSize: 13
        }
      }
    ]
  }
  const myChart = echarts.init(chartRef.value)
  myChart.setOption(option)
}

</script>
<style scoped>
/* 设置CSS变量 - 使用全局作用域 */
:deep(.el-table) {
  --even-group-row-color: #edf6ff;
  --odd-group-row-color: #ffffff;
}

.bodybg {
  background: url(/images/zyfw_bg1.png) no-repeat center 76px #edf6ff;
  background-size: cover;
  min-width: 320px;
  /* 改为移动端友好 */
  overflow: hidden;
}

.box {
  transform: scale(0.95);
  transform-origin: 0 0;
}

.topbg_industry {
  background: url(/images/zyfw_topbg.png) no-repeat top;
  background-size: cover;
  height: 55px;
  width: 100%;
}

.backbg {
  background: url(/images/zyfw_back.png) no-repeat top;
  background-size: contain;
  width: 155px;
  height: 26px;
  color: #fff;
  text-align: center;
  font-size: 16px;
  line-height: 27px;
}

.b_l {
  padding-bottom: 12px;
  border-bottom: 3px solid;
  border-image: linear-gradient(to right, #36b0ff, #0080d4) 1;
  font-weight: 500;
}

.left {
  width: 28.5rem;
}


:deep(.el-table .cell) {
  color: #303133;
}

:deep(.el-table td.el-table__cell) {
  border-right: solid 1px #fff;
}

:deep(.el-table th.el-table__cell .cell) {
  background-color: #8bd3f7;
  color: #fff;
  padding: 6px 0;
  padding-left: 25px;
}

:deep(.el-table .cell) {
  color: #212121;
  padding-left: 25px;
  white-space: nowrap;
  word-break: keep-all;
}





:deep(.el-table__body td:nth-child(1)) {
  border-right: solid 1px #e9eced;
}

.h-20 {
  height: 20rem;
}

.flex-container {
  /* display: flex; */
  /* flex-wrap: wrap; */
  gap: 1rem;
}

.flex-item {
  flex: 0 0 90%;
  /* 最小320px */
  min-width: 450px;
  /* 移动端友好 */
}

.main {
  flex: 2 1 640px;
  min-width: 640px;
  /* 移动端友好 */
}
:deep(.el-table td.el-table__cell){border-right: solid 1px #d8e9f0;}
@media (max-width: 1024px) {
  .main {
    /* order: 0; 中间内容优先显示 */
    flex-basis: 100%;
  }
}

:deep(.demo-form-inline .el-select) {
  --el-select-width: 150px;
}

:deep(.custom-date-picker) {
  width: 150px;
  --el-date-editor-width: 100%;
  /* 覆盖内部变量 */
  height: 26px;
  font-size: 12px;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 10px;
  margin-bottom: 5px;
}

:deep(.el-select__wrapper) {
  min-height: 26px;
  font-size: 12px;
}

.custom-height-btn {
  background: linear-gradient(90deg, #36b0ff 0%, #0080d4 100%);
  height: 26px;
  line-height: 26px;
  /* 确保文字垂直居中 */
  padding: 0 10px;
  /* 调整左右内边距 */
  font-size: 12px;
}

.custom-height-btn:hover {
  background: linear-gradient(90deg, #159af0 0%, #0080d4 100%);
}

:deep(.el-table) {
  background: rgba(255, 255, 255, 0.3);
  border-collapse: separate;
  border-spacing: 0;
}

:deep(.el-table--border th.el-table__cell) {
  border: none !important;
}

/* 合并单元格样式 */
:deep(.el-table .el-table__body .merged-cell) {
  background: linear-gradient(to bottom, #f0f9ff, #e6f7ff) !important;
  border-right: 1px solid #c3e6ff !important;
  font-weight: 500;
}

/* 交替背景色样式 */
:deep(.merged-cell-even) {
  background-color: var(--even-group-row-color) !important;
  font-weight: 500;
  border-right: 1px solid #d8e9f0 !important;
}

:deep(.merged-cell-odd) {
  background-color: var(--odd-group-row-color) !important;
  font-weight: 500;
  border-right: 1px solid #c3e6ff !important;
}

:deep(.even-group-row) {
  background-color: var(--even-group-row-color) !important;
}

:deep(.odd-group-row) {
  background-color: var(--odd-group-row-color) !important;
}

:deep(.even-group-cell) {
  background-color: var(--even-group-row-color) !important;
}

:deep(.odd-group-cell) {
  background-color: var(--odd-group-row-color) !important;
}


</style>