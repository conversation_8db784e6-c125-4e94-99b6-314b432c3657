<template>
    <div :class="{ 'px-[20px] py-[20px]': !isFullscreen }" class=" box">
        <div class="bodybg relative" :style="{ height: isFullscreen ? `820px` : '560px' }">
            <div class="topbg_industry relative mx-auto w-full flex justify-center">
                <h1 class="ys_font mt-[2px] text-[30px] text-white tracking-[10px]">
                    世界核电
                </h1>
                <div class="backbg absolute right-[30px] top-[10px]">
                    <a class="text-[#fff] hover:color-[#318AD9] cursor-pointer" @click="toggleFullscreen()">
                        <span v-if="!isFullscreen">全屏</span>
                        <span v-else>退出全屏</span>
                    </a>
                </div>
            </div>
            <div class="flex-container mt-[10px] px-[15px] box"
                :style="{ transform: isFullscreen ? `scale(1)` : 'scale(0.64)' }">
                <div class="flex-item">
                    <div class="overflow-hidden bg-[rgba(255,255,255,0.5)]">
                        <h2 class="bg_menu mt-[5px] flex justify-between">
                            <div class="flex">
                                <img src="/images/zyfz_icon.png" class="ml-[10px] mt-[8px] h-[17px] w-[15px]"><span
                                    class="ml-[5px] mt-[2px]"
                                    :style="{ 'font-size': isFullscreen ? `18px` : '21px' }">在运情况</span>
                            </div><a
                                class="mr-[20px] mt-[6px] text-sm color-[#469EE4] hover:color-[#15aff9] cursor-pointer"
                                @click="$router.push({ path: '/industry/global/data/erji-operational' })"
                                :style="{ 'font-size': isFullscreen ? `14px` : '18px' }">更多>></a>
                        </h2>
                        <el-table :data="operList" stripe height="310"
                            :style="{ 'font-size': isFullscreen ? `15px` : '20px' }">
                            <el-table-column prop="country" label="国家和地区" width='115px'>
                                <template #default="{ row }">
                                    {{ useCountry().getCountryChineseName(row.country) }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="reactors" label="数量(台)" width='100px' />
                            <el-table-column prop="capacityTotal" label="装机容量(mv)" />
                        </el-table>
                    </div>
                    <div class="mt-[15px] h-[355px] overflow-hidden bg-[rgba(255,255,255,0.5)]">
                        <h2 class="bg_menu mt-[10px] flex justify-between">
                            <div class="flex">
                                <img src="/images/zyfz_icon.png" class="ml-[10px] mt-[8px] h-[17px] w-[15px]"><span
                                    class="ml-[5px] mt-[2px]"
                                    :style="{ 'font-size': isFullscreen ? `18px` : '21px' }">年度主要变化情况统计</span>
                            </div><a
                                class="mr-[20px] mt-[7px] text-sm color-[#469EE4] hover:color-[#15aff9] cursor-pointer"
                                @click="$router.push({ path: '/industry/global/data/erji-annual' })"
                                :style="{ 'font-size': isFullscreen ? `14px` : '18px' }">更多>></a>
                        </h2>
                        <div ref="chartRef" class="flex justify-center p-[10px] pt-[20px]" style="height: 300px">
                            <!-- <img src="/images/charts1.png" style="object-fit: cover; height:15rem"> -->
                        </div>
                    </div>
                </div>
                <div class="flex-item main">
                    <div class="mt-[15px] flex justify-center relative ">
                        <div class="h-[300px]"><img src="/images/zyfz_map.png"
                                style="object-fit:contain; height: 18rem;"></div>
                        <!-- <div style="height:25rem"></div> -->

                    </div>
                    <div class="mt-[47px] h-[350px] overflow-hidden bg-[rgba(255,255,255,0.5)]">
                        <h2 class="bg_menu mt-[10px] flex justify-between">
                            <div class="flex">
                                <img src="/images/zyfz_icon.png" class="ml-[10px] mt-[8px] h-[17px] w-[15px]"><span
                                    class="ml-[5px] mt-[2px]"
                                    :style="{ 'font-size': isFullscreen ? `18px` : '21px' }">各国核电发电量占比</span>
                            </div><a
                                class="mr-[20px] mt-[6px] text-sm color-[#469EE4] hover:color-[#15aff9] cursor-pointer"
                                @click="$router.push({ path: '/industry/global/data/erji-power' })"
                                :style="{ 'font-size': isFullscreen ? `14px` : '18px' }">更多>></a>
                        </h2>
                        <div ref="fdzb1Ref" class="flex justify-center p-[10px] h-full">
                            <!-- <img src="/images/chart2.png" style="object-fit: cover; height:15rem;margin-top:10px;"> -->
                        </div>
                    </div>
                </div>
                <div class="flex-item">
                    <div class="overflow-hidden bg-[rgba(255,255,255,0.5)]">
                        <h2 class="bg_menu flex justify-between">
                            <div class="flex">
                                <img src="/images/zyfz_icon.png" class="ml-[10px] mt-[8px] h-[17px] w-[15px]"><span
                                    class="ml-[5px] mt-[2px]"
                                    :style="{ 'font-size': isFullscreen ? `18px` : '21px' }">在建情况</span>
                            </div><a
                                class="mr-[20px] mt-[6px] text-sm color-[#469EE4] hover:color-[#15aff9] cursor-pointer"
                                @click="$router.push({ path: '/industry/global/data/erji-construction' })"
                                :style="{ 'font-size': isFullscreen ? `14px` : '18px' }">更多>></a>
                        </h2>
                        <el-table :data="underList" stripe height="310"
                            :style="{ 'font-size': isFullscreen ? `15px` : '20px' }">
                            <el-table-column prop="country" label="国家和地区" width='115px'>
                                <template #default="{ row }">
                                    {{ useCountry().getCountryChineseName(row.country) }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="reactors" label="数量(台)" width='100px' />
                            <el-table-column prop="capacityTotal" label="装机容量(mv)" />
                        </el-table>
                    </div>
                    <div class="mt-[17px] h-[350px] overflow-hidden bg-[rgba(255,255,255,0.5)]">
                        <h2 class="bg_menu mt-[10px] flex justify-between">
                            <div class="flex">
                                <img src="/images/zyfz_icon.png" class="ml-[10px] mt-[8px] h-[17px] w-[15px]"><span
                                    class="ml-[5px] mt-[4px] h-[25px]" :class="{ 'w-[100%]': !isFullscreen }"
                                    :style="{ 'font-size': isFullscreen ? `16px` : '16px' }">各国核能发电占全球核能发电比例</span>
                            </div><a
                                class="mr-[5px] mt-[6px] text-sm color-[#469EE4] hover:color-[#15aff9] cursor-pointer"
                                :class="{ 'w-[20%]': !isFullscreen }"
                                @click="$router.push({ path: '/industry/global/data/erji-nuclear' })"
                                :style="{ 'font-size': isFullscreen ? `14px` : '18px' }">更多>></a>
                        </h2>
                        <div class="overflow-y-auto h-[340px]">
                            <div ref="fdzb2Ref" class="flex justify-center p-[10px] h-full ">
                                <!-- <img src="/images/chart3.png" style="object-fit: cover; height:15rem;margin-top:10px;"> -->
                            </div>
                        </div>

                    </div>
                </div>
            </div>

            <div class="absolute bottom-[10px]  text-[14px]" :style="{ right: isFullscreen ? `10px` : '6px' }">数据来源于IAEA、中国核能行业协会，截至{{ lastUpdateDate }}</div>
        </div>
    </div>

</template>

<script setup lang="ts">
import type { LayoutKey } from '#build/types/layouts';
import { getFdblList, getNuclearList, getOperList, getUnderList, getWorldUpdateDeadline, getYearChangeList, type IFdblList, type INuclearList, type IOperList, type IUnderList, type IYearChangeList } from '~/api/crawler/global';
import { ECOLUMN_CODE } from '~/store/column';
import * as echarts from 'echarts'
import { groupBy } from 'lodash';
import chroma from 'chroma-js'
const { isFullscreen, toggleFullscreen, exitFullscreen } = useFullscreenLayout()

const layout = ref<LayoutKey | false>('column');
definePageMeta({
    name: ECOLUMN_CODE.INDUSTRY_GLOBAL_DATA,
    layout: layout,
})

const underList = ref<IUnderList[]>([])
getUnderList().then(res => {
    underList.value = res.rows ?? []
})

const operList = ref<IOperList[]>([])
getOperList().then(res => {
    operList.value = res.rows ?? []
})

const fdzb1Ref = ref<HTMLDivElement>()
// const nuclearList = ref<INuclearList[]>([])
getNuclearList().then(res => {
    // 1. 对原始数据按 nuclearShare 降序排序
    const sortedRows = [...(res.rows || [])].sort((a, b) => b.nuclearShare - a.nuclearShare);
    // 2. 提取排序后的数据
    const xAxisData = sortedRows.map(t => useCountry().getCountryChineseName(t.country));
    const seriesData = sortedRows.map(t => t.nuclearShare);
    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            textStyle: {
                fontSize: 20,       // 字体大小（单位px）[4,8](@ref)
            },
            formatter: function (params: any) {
                // 取第一个数据项（柱状图单系列下 params 是数组）
                const item = params[0];
                // 组合内容：国家名称 + 百分比值 + '%'
                return `<div>
                <p style="text-align: left"><strong>核电发电量占比</strong></p>
                <div style="text-align: left">
                  ${item.name}: ${item.value}%
                </div>
              </div>`;

               
            }
        },
        grid: {
            left: '2%',
            right: '3%',
            top: '8%',
            bottom: '25%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: xAxisData,
            axisLabel: { fontSize: 14, show: false },
            // axisLabel: {
            //     interval: 0,       // 强制显示所有标签
            //     rotate: 0,        // 标签旋转45度
            //     overflow: 'truncate' // 超出部分截断
            // }
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
                data: seriesData,
                // 设置x轴标签字体大小 
                type: 'bar',
                barWidth: 10,
                itemStyle: {
                    color: function (params: any) {
                        const countryName = xAxisData[params.dataIndex];
                        if (countryName === '中国') {
                            return '#ea3e2c';
                        }
                        // 非中国返回渐变色
                        return {
                            type: 'linear',
                            x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [
                                { offset: 0, color: '#0099FC' },
                                { offset: 1, color: '#188df0' }
                            ]
                        };
                    },

                    borderRadius: [10, 10, 0, 0] // 左上 右上 右下 左下 的圆角半径
                }

            }
        ]
    };
    const myChart = echarts.init(fdzb1Ref.value)
    myChart.setOption(option)
})

const chartRef = ref<HTMLDivElement>()
getYearChangeList().then(res => {
    const groupData = groupBy(res.rows, 'status')

    // 渐变生成器
    const generateGradient = (startColor: string, endColor: string, direction: any) => {
        const dirMap: { [key: string]: number[] } = {
            right: [0, 0, 1, 0],   // From left to right
            bottom: [0, 0, 0, 1],  // From top to bottom
            diagonal: [0, 0, 1, 1] // Diagonal
        };

        const gradientDirection = dirMap[direction] || dirMap['right'];

        // @ts-ignore
        return new echarts.graphic.LinearGradient(...gradientDirection as any, [
            { offset: 0, color: startColor },
            { offset: 1, color: endColor }
        ]);
    }

    // 定义渐变配色方案
    const GRADIENTS = [
        { start: '#5175FD', end: '#52AEFF', dir: 'right' },
        { start: '#EB8B00', end: '#ECB906', dir: 'diagonal' },
        { start: '#67D98D', end: '#5ACDD1', dir: 'bottom' },
        { start: '#E83C31', end: '#FF6461', dir: 'right' }
    ]

    const option = {
        title: {
            show: false
        },
        tooltip: {
            textStyle: { fontSize: 20 },
            trigger: 'item',
            formatter: function (params: any) { // 使用回调函数动态格式化
                const value = params.value; // 原始数值
                const name = params.name;   // 数据项名称
                const percent = params.percent; // 自动计算的百分比（需手动格式化）

                // 保留1位小数 + 添加百分号
                const formattedPercent = percent !== null
                    ? percent.toFixed(1) + '%'
                    : 'N/A'; // 异常处理

                return `${name}<br/>${value} (${formattedPercent})`;
            }
        },

        series: [
            {
                name: '数量',
                type: 'pie',
                radius: ['20%', '50%'],
                data: Object.keys(groupData).map((t, i) => {
                    return {
                        name: EHd_Year_change_map[t] ?? t,
                        value: groupData[t].length,
                        itemStyle: {
                            color: generateGradient(
                                GRADIENTS[i % 4].start,
                                GRADIENTS[i % 4].end,
                                GRADIENTS[i % 4].dir
                            )
                        }
                    }
                }),
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                label: {
                    show: true,
                    formatter: function (params: any) {
                        return `${params.name}\n${(params.percent ?? 0).toFixed(1)}%`;
                    },// 饼图上显示名称和百分比
                    color: '#222',
                    fontSize: 14,
                    textBorderColor: '#000',
                    textBorderWidth: 0
                },
                labelLine: {
                    show: true // 显示标签引导线
                }
            }
        ]
    }

    const myChart = echarts.init(chartRef.value!)
    myChart.setOption(option)

    // 响应式调整
    window.addEventListener('resize', () => myChart.resize())
})
const fdzb2Ref = ref<HTMLDivElement>()
const fablList = ref<IFdblList[]>([])
getFdblList().then(res => {
    fablList.value = res.rows ?? [];

    // 1. 计算数据总和（用于计算百分比）
    const total = fablList.value.reduce((sum, item) => sum + item.capacityTotal, 0);

    const option = {
        color: ['#DE66AE', '#EC8ACE', '#E6BBF0', '#9C95F4', '#7F79E7', '#95BEFF', '#95BEFF', '#38A1D8', '#32C2E7', '#64DEDF', '#9EE4BE', '#C4F76B', '#EBF969', '#FCD969', '#FFA66E', '#F6788B'],
        tooltip: {
            trigger: 'item',
            textStyle: {

                fontSize: 20,       // 字体大小（单位px）[4,8](@ref)


            },
            formatter: function (params: any) { // 使用回调函数动态格式化
                const value = params.value; // 原始数值
                const name = params.name;   // 数据项名称
                const percent = params.percent; // 自动计算的百分比（需手动格式化）


                // 保留1位小数 + 添加百分号
                const formattedPercent = percent !== null
                    ? percent.toFixed(1) + '%'
                    : 'N/A'; // 异常处理

                return `${name}<br/>${value} (${formattedPercent})`;
            }
        },
        legend: { show: false },
        series: [{
            name: '容量',
            type: 'pie',
            center: ['50%', '30%'],
            radius: ['15%', '30%'],
            data: res.rows?.map(t => {
                const percentage = (t.capacityTotal / total) * 100;
                const showLabel = percentage >= 1; // 仅当≥1%时显示

                return {
                    value: t.capacityTotal,
                    name: useCountry().getCountryChineseName(t.country),
                    // 关键配置：动态控制标签和引导线
                    label: {
                        show: showLabel, // 标签显隐控制[5](@ref)
                        formatter: function (params: any) {
                            return `${params.name}${(params.percent ?? 0).toFixed(1)}%`;
                        },

                        color: '#222',
                        fontSize: 14
                    },
                    labelLine: {
                        show: showLabel // 引导线显隐控制[1,5](@ref)
                    }
                };
            }),
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }]
    };

    const myChart = echarts.init(fdzb2Ref.value);
    myChart.setOption(option);
});

const lastUpdateDate = ref('')
getWorldUpdateDeadline().then(res => {
    lastUpdateDate.value = res.msg
})
</script>
<style scoped>
.bodybg {
    background: url(/images/zyfw_bg1.png) no-repeat center 76px #D9F2FF;
    background-size: cover;
    min-width: 320px;
    /* 改为移动端友好 */
    overflow: hidden;
}

.menubg {
    background: linear-gradient(180deg, #54a3e2 0%, #3b97e0 100%);
    border-radius: 5px 5px 0px 0px;
    text-align: left;
}

.menubg a {
    color: #fff;
    padding: 5px 20px;
    margin: 4px 10px 0 10px;
    cursor: pointer;
    font-size: 18px;
    display: inline-block;
}

.menubg a:hover {
    color: #c9e7ff;
}

.menubg a.hover {
    background-color: #fff;
    color: #469ee4;
    display: inline-block;
    border-radius: 5px 3px 0 0;
}

.box {
    transform: scale(1);
    transform-origin: 0 0;
}

.topbg_industry {
    background: url(/images/zyfw_topbg.png) no-repeat top;
    background-size: cover;
    height: 55px;
    width: 100%;
}

.backbg {
    background: url(/images/zyfw_back.png) no-repeat top;
    background-size: contain;
    width: 155px;
    height: 26px;
    color: #fff;
    text-align: center;
    font-size: 16px;
    line-height: 27px;
}

.b_l {
    padding-bottom: 12px;
    border-bottom: 3px solid;
    border-image: linear-gradient(to right, #36b0ff, #0080d4) 1;
    font-weight: 500;
}

.left {
    width: 28.5rem;
}

:deep(.el-table) {
    background: rgba(255, 255, 255, 0.3);
}

:deep(.el-table .cell) {
    color: #303133;
}

:deep(.el-table .el-table__cell) {}

:deep(.el-table td.el-table__cell) {
    border-bottom: none;
}

:deep(.el-table th.el-table__cell .cell) {
    background-color: #8bd3f7;
    color: #fff;
    padding: 6px 0;
    padding-left: 25px;
}

:deep(.el-table .cell) {
    color: #212121;
    padding-left: 25px;
    white-space: nowrap;
    word-break: keep-all;
}

:deep(.el-table tr) {
    background-color: rgba(255, 255, 255, 0.5);
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell) {
    background-color: rgba(220, 243, 251, 0.5);
}

.h-20 {
    height: 20rem;
}

.flex-container {
    display: flex;
    /* flex-wrap: wrap; */
    gap: 1rem;
}

.flex-item {
    flex: 1 1 300px;
}

.main {
    flex: 2 1 440px;
    /* min-width: 640px; 移动端友好 */
}
</style>