<template>
  <div :class="{ 'px-[20px] py-[20px]': !isFullscreen }" class="box"
    :style="{ transform: isFullscreen ? `scale(1)` : 'scale(1)' }">
    <div class="bodybg" :style="{ height: isFullscreen ? `880px` : '950px' }">
      <div class="topbg_industry relative mx-auto w-full flex justify-center">
        <h1 class="ys_font mt-[2px] text-[30px] text-white tracking-[10px]">
          世界核电
        </h1>
        <div class="backbg absolute right-[150px] top-[10px] mr-[15px] cursor-pointer">
          <a class="text-[#fff] hover:color-[#318AD9] cursor-pointer" @click="toggleFullscreen()">
            <span v-if="!isFullscreen">全屏</span>
            <span v-else>退出全屏</span>
          </a>
        </div>
        <div class="backbg absolute right-[30px] top-[10px] cursor-pointer">
          <a class="text-[#fff] hover:color-[#318AD9]" @click="$router.back()">返回</a>
        </div>
      </div>
      <div class="flex-container mt-[10px] justify-center">
        <div class="flex-item">
          <h1 class="ys_font mt-[10px] text-[22px] text-[#4F5D69] font-500 text-center">
            各国核能发电占全球核能发电比例
          </h1>
          <el-form :inline="true"
            class="flex justify-center demo-form-inline mt-[10px] border border-[#b6e3f9] bg-[rgba(217,242,255,0.4)] pt-[5px]">
            <el-form-item label="国家和地区">
              <el-select v-model="searchForm.country" placeholder="选择国家和地区" size="small" filterable clearable>
                 <el-option v-for="item in list_gj " :label="useCountry().getCountryChineseName(item.value!)" :value="item.value!" />
              </el-select>
            </el-form-item>
            <el-form-item label="年度">
              <el-date-picker v-model="searchForm.searchDate" type="year" value-format="YYYY" placeholder="选择年度" clearable
                class="custom-date-picker" :disabled-date="(date: Date) => dayjs(date).isAfter(dayjs(), 'year')" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :icon="Search" class="custom-height-btn" @click="search">
                查询
              </el-button>
            </el-form-item>
          </el-form>
          <el-table :data="fablList" stripe height="665" :style="{ 'font-size': isFullscreen ? `15px` : '16px' }">
            <el-table-column type="index" width="70" label="序号" />
            <el-table-column prop="country" label="国家" width="120" sortable>
              <template #default="{ row }">
                {{ useCountry().getCountryChineseName(row.country) }}
              </template>
            </el-table-column>
            <el-table-column prop="suppliedTotalGwh" label="核电总净发电容量(MW)" sortable/>
            <el-table-column prop="reactors" label="反应堆数量" sortable/>
            <el-table-column prop="gwhBl" label="占比(%)" sortable/>
          </el-table>
        </div>
      </div>

    </div>
  </div>

</template>

<script lang="ts" setup>
import {
  Search,
} from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { reactive } from 'vue'
import { getFdblList, getWorldDict, type IFdblList, type  Operational } from '~/api/crawler/global'
const { isFullscreen, toggleFullscreen } = useFullscreenLayout()

const searchForm = reactive({
  country: '',
  searchDate: '',
})
const fablList = ref<IFdblList[]>([])
const searchForm_gj = reactive({
  status: '1',
}as Operational) 
const list_gj = ref<Operational[]>([])
const search = () => {
  getFdblList(searchForm).then(res => {
    fablList.value = res.rows ?? []
  })
   getWorldDict(searchForm_gj).then(res => {
    list_gj.value = res.data ?? []
  })
}
search()
</script>
<style scoped>
.bodybg {
  background: url(/images/zyfw_bg1.png) no-repeat center 76px #edf6ff;
  background-size: cover;
  min-width: 320px;
  /* 改为移动端友好 */
  overflow: hidden;
}

.box {
  transform: scale(1);
  transform-origin: 0 0;
}

.topbg_industry {
  background: url(/images/zyfw_topbg.png) no-repeat top;
  background-size: cover;
  height: 55px;
  width: 100%;
}

.backbg {
  background: url(/images/zyfw_back.png) no-repeat top;
  background-size: contain;
  width: 155px;
  height: 26px;
  color: #fff;
  text-align: center;
  font-size: 16px;
  line-height: 27px;
}

.b_l {
  padding-bottom: 12px;
  border-bottom: 3px solid;
  border-image: linear-gradient(to right, #36b0ff, #0080d4) 1;
  font-weight: 500;
}

.left {
  width: 28.5rem;
}

:deep(.el-table) {
  background: rgba(255, 255, 255, 0.3);
}

:deep(.el-table .cell) {
  color: #303133;
}

:deep(.el-table .el-table__cell) {}

:deep(.el-table td.el-table__cell) {
  border-right: solid 1px #fff;
}

:deep(.el-table th.el-table__cell .cell) {
  background-color: #8bd3f7;
  color: #fff;
  padding: 6px 0;
  padding-left: 25px;
}

:deep(.el-table .cell) {
  color: #212121;
  padding-left: 25px;
  white-space: nowrap;
  word-break: keep-all;
}

:deep(.el-table tr) {
  background-color: rgba(255, 255, 255, 0.5);
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell) {
  background-color: rgba(220, 243, 251, 0.5);
}
:deep(.el-table td.el-table__cell){
  border-right:none;
}
.h-20 {
  height: 20rem;
}

.flex-container {
  display: flex;
  /* flex-wrap: wrap; */
  gap: 1rem;
}

.flex-item {
  flex: 0 0 80%;
  /* 最小320px */
  min-width: 450px;
  /* 移动端友好 */
}

@media (max-width: 1024px) {
  .main {
    /* order: 0; 中间内容优先显示 */
    flex-basis: 100%;
  }
}

:deep(.demo-form-inline .el-select) {
  --el-select-width: 150px;
}

:deep(.custom-date-picker) {
  width: 150px;
  --el-date-editor-width: 100%;
  /* 覆盖内部变量 */
  height: 26px;
  font-size: 12px;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 10px;
  margin-bottom: 5px;
}

:deep(.el-select__wrapper) {
  min-height: 26px;
  font-size: 12px;
}

.custom-height-btn {
  background: linear-gradient(90deg, #36b0ff 0%, #0080d4 100%);
  height: 26px;
  line-height: 26px;
  /* 确保文字垂直居中 */
  padding: 0 10px;
  /* 调整左右内边距 */
  font-size: 12px;
}

.custom-height-btn:hover {
  background: linear-gradient(90deg, #159af0 0%, #0080d4 100%);
}
:deep(.el-form-item__label) {
    font-size: 16px;
  }
:deep(.custom-height-btn){
   font-size: 14px;
}
:deep(.el-select__input){
   font-size: 16px;
}
</style>