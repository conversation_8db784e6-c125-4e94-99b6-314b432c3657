<template>
  <div :class="{ 'px-[20px] py-[20px]': !isFullscreen }">
    <div class="bodybg" :style="{ height: isFullscreen ? `850px` : '760px' }">
      <div class="topbg_industry relative mx-auto w-full flex justify-center">
        <h1 class="ys_font mt-[2px] text-[30px] text-white tracking-[10px]">
          世界核电
        </h1>
        <div class="backbg absolute right-[150px] top-[10px] mr-[15px] cursor-pointer">
          <a class="text-[#fff] hover:color-[#318AD9] cursor-pointer" @click="toggleFullscreen()">
            <span v-if="!isFullscreen">全屏</span>
            <span v-else>退出全屏</span>
          </a>
        </div>
        <div class="backbg absolute right-[30px] top-[10px] cursor-pointer">
          <a class="text-[#fff] hover:color-[#318AD9]" @click="$router.back()">返回</a>
        </div>
      </div>
      <div class="flex-container mt-[10px] px-[15px] box"
        :style="{ transform: isFullscreen ? `scale(1)` : 'scale(0.77)' }">
        <div class="flex-item">
          <div class="h-[752px] overflow-hidden bg-[rgba(255,255,255,0.5)]">
            <h2 class="bg_menu mt-[5px] flex justify-between">
              <div class="flex">
                <img src="/images/zyfz_icon.png" class="ml-[10px] mt-[8px] h-[17px] w-[15px]"><span
                  class="ml-[5px] mt-[5px]">核电发电量及占比</span>
              </div>
            </h2>
            <div ref="chartRef" class="mt-[20px] flex justify-center p-[10px] pt-[20px]" style="height: 300px">
              <img src="/images/chart5.png" style="object-fit: cover; height:18rem">
            </div>
            <div ref="chart1Ref" class="mt-[10px] flex justify-center p-[10px] pt-[20px] ml-[10px]"
              style="height: 400px">
              <img src="/images/chart6.png" style="object-fit: cover; height:18rem">
            </div>
          </div>
        </div>
        <div class="flex-item main">
          <h1 class="ys_font mt-[10px] text-[22px] text-[#4F5D69] font-500 text-center">
            年度核电发电量占比
          </h1>
          <el-form :inline="true"
            class="flex justify-center demo-form-inline mt-[10px] border border-[#b6e3f9] bg-[rgba(217,242,255,0.4)] pt-[5px]">
            <el-form-item label="国家和地区">
              <el-select v-model="searchForm.country" placeholder="选择国家和地区" size="small" filterable clearable>
                <el-option v-for="item in list_gj" :label="useCountry().getCountryChineseName(item.value!)"
                  :value="item.value!" />
              </el-select>
            </el-form-item>
            <el-form-item label="统计年份">
              <el-date-picker v-model="searchForm.searchDate" type="year" value-format="YYYY" placeholder="选择年度"
                clearable class="custom-date-picker" :disabled-date="(date: Date) => dayjs(date).isAfter(dayjs(), 'year')" />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" :icon="Search" class="custom-height-btn" @click="search">
                查询
              </el-button>
            </el-form-item>
          </el-form>
          <el-table :data="nuclearList" stripe height="665" :style="{ 'font-size': isFullscreen ? `15px` : '17px' }">
            <el-table-column type="index" width="70" label="序号" />
            <el-table-column prop="id" label="变化情况" width="180" sortable>
              <template #default="{ row }">
                {{ useCountry().getCountryChineseName(row.country) }}
              </template>
            </el-table-column>
            <el-table-column prop="reactors" label="机组数量(台)" sortable />
            <el-table-column prop="suppliedTotalGwh" label="核电发电量(GW.h)" sortable />
            <el-table-column prop="nuclearShare" label="核电发电量占比(%)" sortable />
          </el-table>
        </div>
      </div>

    </div>
  </div>

</template>

<script lang="tsx" setup>
import {
  Search,
} from '@element-plus/icons-vue'
import { reactive } from 'vue'
import { getNuclearList, getWorldDict, type INuclearList, type Operational } from '~/api/crawler/global'
import * as echarts from 'echarts'
import dayjs from 'dayjs'

const { isFullscreen, toggleFullscreen } = useFullscreenLayout()

const searchForm = reactive({
  country: '',
  searchDate: '',
})

const searchForm_gj = reactive({
  status: '2',
} as Operational)
const list_gj = ref<Operational[]>([])
const nuclearList = ref<INuclearList[]>([])
const search = () => {
  getNuclearList(searchForm).then(res => {
    nuclearList.value = res.rows ?? []
    chart(nuclearList.value)
    chart1(nuclearList.value)
  })
  getWorldDict(searchForm_gj).then(res => {
    list_gj.value = res.data ?? []
  })
}
search()

const chartRef = ref()
const chart = (data: INuclearList[]) => {
const total = data.reduce((sum, item) => sum + item.capacityTotal, 0);
  const option = {
    title: {
      show: false
    },
     tooltip: {
            trigger: 'item',
             textStyle: {fontSize: 20,   },
            // 显示名称+数据+百分比
            formatter: '{b}<br/> {c} ({d}%)'  // {a}系列名, {b}数据名, {c}数值, {d}百分比
        },
    legend: {
      show: false
    },
    series: [
      {
        name: '核电发电量占比',
        type: 'pie',
        radius: '50%',
       


  data: data.map(t => {
                const percent = (t.capacityTotal / total) * 100;
                const showLabel = percent >= 1; // 仅当≥1%时显示标签
                
                return {
                    value: t.capacityTotal,
                    name: useCountry().getCountryChineseName(t.country),
                    // 动态控制标签和引导线
                     label: {
                        show: showLabel,
                        formatter: function(params:any) {
        return `${params.name}${(params.percent ?? 0).toFixed(1)}%`;},
                        textBorderColor: '#000',
                        textBorderWidth: 0,
                        color: '#222',
                        fontSize: 13
                    },
                    labelLine: {
                        show: showLabel // 同步隐藏引导线
                    }
                };
            }),




        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          formatter: '{b}', // 添加百分比显示
          textBorderColor: '#000', // 文字描边颜色
          textBorderWidth: 0,
          color: '#222',
          fontSize: 13
        }
      }
    ]
  }
  const myChart = echarts.init(chartRef.value)
  myChart.setOption(option)
}

const chart1Ref = ref()
const chart1 = (data: INuclearList[]) => {
  // 1. 数据排序处理
  const sortedData = [...data] // 创建数据副本
    .sort((a, b) => b.nuclearShare - a.nuclearShare) // 按核能占比降序排列

  const option = {
    grid: { left: '10%', right: '5%' },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      textStyle: {fontSize: 20,   },
      formatter: function (params: any) {
        console.log(params)
        const countryName = params.at(0).name;
        const value = params.at(0).value;
        return `<div>
                <p style="text-align: left"><strong>核电发电量占比</strong></p>
                <div>
                  ${countryName}: ${value}%
                </div>
              </div>`;
      }
    },
    xAxis: {
      type: 'category',
      data: sortedData.map(t => useCountry().getCountryChineseName(t.country)),
      axisLabel: { fontSize: 14,show: false },
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        data: sortedData.map(t => t.nuclearShare),
        type: 'bar'
      }
    ]
  };
  const myChart = echarts.init(chart1Ref.value)
  myChart.setOption(option)
}
</script>
<style scoped>
.bodybg {
  background: url(/images/zyfw_bg1.png) no-repeat center 76px #edf6ff;
  background-size: cover;
  min-width: 320px;
  /* 改为移动端友好 */
  overflow: hidden;
}

.box {
  transform: scale(0.95);
  transform-origin: 0 0;
}

.topbg_industry {
  background: url(/images/zyfw_topbg.png) no-repeat top;
  background-size: cover;
  height: 55px;
  width: 100%;
}

.backbg {
  background: url(/images/zyfw_back.png) no-repeat top;
  background-size: contain;
  width: 155px;
  height: 26px;
  color: #fff;
  text-align: center;
  font-size: 16px;
  line-height: 27px;
}

.b_l {
  padding-bottom: 12px;
  border-bottom: 3px solid;
  border-image: linear-gradient(to right, #36b0ff, #0080d4) 1;
  font-weight: 500;
}

.left {
  width: 28.5rem;
}

:deep(.el-table) {
  background: rgba(255, 255, 255, 0.3);
}

:deep(.el-table .cell) {
  color: #303133;
}

:deep(.el-table .el-table__cell) {}

:deep(.el-table td.el-table__cell) {}

:deep(.el-table th.el-table__cell .cell) {
  background-color: #8bd3f7;
  color: #fff;
  padding: 6px 0;
  padding-left: 25px;
}

:deep(.el-table .cell) {
  color: #212121;
  padding-left: 25px;
  white-space: nowrap;
  word-break: keep-all;
}

:deep(.el-table tr) {
  background-color: rgba(255, 255, 255, 0.5);
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell) {
  background-color: rgba(220, 243, 251, 0.5);
}

:deep(.el-table__body td:nth-child(1)) {
  border-right: solid 1px #e9eced;
}

:deep(.el-table__body td:nth-child(1)) {
  border-right: none;
}

.h-20 {
  height: 20rem;
}

.flex-container {
  display: flex;
  /* flex-wrap: wrap; */
  gap: 1rem;
}

.flex-item {
  flex: 0 0 500px;
  /* 最小320px */
  min-width: 500px;
  /* 移动端友好 */
}

.main {
  flex: 2 1 590px;
  min-width: 590px;
  /* 移动端友好 */
}

@media (max-width: 1024px) {
  .main {
    /* order: 0; 中间内容优先显示 */
    flex-basis: 100%;
  }
}

:deep(.demo-form-inline .el-select) {
  --el-select-width: 150px;
}

:deep(.custom-date-picker) {
  width: 150px;
  --el-date-editor-width: 100%;
  /* 覆盖内部变量 */
  height: 26px;
  font-size: 12px;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 10px;
  margin-bottom: 5px;
}

:deep(.el-select__wrapper) {
  min-height: 26px;
  font-size: 12px;
}

.custom-height-btn {
  background: linear-gradient(90deg, #36b0ff 0%, #0080d4 100%);
  height: 26px;
  line-height: 26px;
  /* 确保文字垂直居中 */
  padding: 0 10px;
  /* 调整左右内边距 */
  font-size: 12px;
}

.custom-height-btn:hover {
  background: linear-gradient(90deg, #159af0 0%, #0080d4 100%);
}

:deep(.el-form-item__label) {
  font-size: 16px;
}

:deep(.custom-height-btn) {
  font-size: 14px;
}

:deep(.el-select__input) {
  font-size: 16px;
}
</style>