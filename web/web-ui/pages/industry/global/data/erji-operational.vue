<template>

  <div :class="{ 'px-[20px] py-[20px]': !isFullscreen }">
    <div class="bodybg pb-[5px] relative" :style="{ height: isFullscreen ? `780px` : '680px' }">
      <div class="topbg_industry relative mx-auto w-full flex justify-center">
        <h1 class="ys_font mt-[2px] text-[30px] text-white tracking-[10px]">
          世界核电
        </h1>
        <div class="backbg absolute right-[150px] top-[10px] mr-[15px] cursor-pointer">
          <a class="text-[#fff] hover:color-[#318AD9] cursor-pointer" @click="toggleFullscreen()">
            <span v-if="!isFullscreen">全屏</span>
            <span v-else>退出全屏</span>
          </a>
        </div>
        <div class="backbg absolute right-[30px] top-[10px] cursor-pointer">
          <a class="text-[#fff] hover:color-[#318AD9]" @click="$router.back()">返回</a>
        </div>
      </div>
      <div class="flex-container mt-[10px] px-[15px] box"
        :style="{ transform: isFullscreen ? `scale(1)` : 'scale(0.75)' }">
        <div class="flex-item">
          <div class="h-[692px] overflow-hidden bg-[rgba(255,255,255,0.5)]">
            <h2 class="bg_menu mt-[5px] flex justify-between">
              <div class="flex">
                <img src="/images/zyfz_icon.png" class="ml-[10px] mt-[8px] h-[17px] w-[15px]"><span
                  class="ml-[5px] mt-[2px]" :style="{ 'font-size': isFullscreen ? `18px` : '20px' }">在运机组数量</span>
              </div>
            </h2>
            <div ref="chartRef" class="mt-[20px] flex justify-center p-[10px] pt-[20px] " style="height: 300px">
              <img src="/images/chart5.png" style="object-fit: cover; height:18rem">
            </div>
            <div ref="chart1Ref" class="flex justify-center p-[10px] mt-[-20px]" style="height: 400px">
              <img src="/images/chart6.png" style="object-fit: cover; height:18rem">
            </div>
          </div>
        </div>
        <div class="flex-item main">
          <h1 class="ys_font mt-[5px] text-[19px] text-[#4F5D69] font-500 text-center"
            :style="{ 'font-size': isFullscreen ? `18px` : '22px' }">
            在运机组数量及装机容量
          </h1>
          <el-form :inline="true" class="demo-form-inline mt-[10px] flex justify-center"
            :style="{ 'font-size': isFullscreen ? '14px' : 'calc(14px / 0.71)' }">
            <el-form-item label="国家和地区">
              <el-select v-model="searchForm.country" placeholder="选择国家和地区" size="small" filterable clearable>
                <!-- <el-option v-for="item in useCountry().getCountryList()" :label="item.label" :value="item.value" /> -->
                <el-option v-for="item in list_gj" :label="useCountry().getCountryChineseName(item.value!)"
                  :value="item.value!" />
              </el-select>
            </el-form-item>
            <el-form-item label="统计时间">
              <el-date-picker v-model="searchForm.searchDate" type="month" value-format="YYYY-MM-DD" placeholder="选择时间"
                clearable class="custom-date-picker" :disabled-date="(date: Date) => dayjs(date).isAfter(dayjs(), 'month')" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :icon="Search" class="custom-height-btn" @click="search">
                查询
              </el-button>
            </el-form-item>
          </el-form>
          <el-table :data="operList" stripe height="603" :style="{ 'font-size': isFullscreen ? `15px` : '18px' }">
            <el-table-column type="index" width="70" label="序号" />
            <el-table-column prop="country" label="国家和地区" sortable>
              <template #default="{ row }">
                {{ useCountry().getCountryChineseName(row.country) }}
              </template>
            </el-table-column>
            <el-table-column prop="reactors" label="机组数量(台)" sortable />
            <el-table-column prop="capacityTotal" label="装机容量(mv)" sortable />
          </el-table>
        </div>
        <div class="flex-item">
          <div class="h-[692px] overflow-hidden bg-[rgba(255,255,255,0.5)]">
            <h2 class="bg_menu flex justify-between">
              <div class="flex">
                <img src="/images/zyfz_icon.png" class="ml-[10px] mt-[8px] h-[17px] w-[15px]"><span
                  class="ml-[5px] mt-[2px]" :style="{ 'font-size': isFullscreen ? `18px` : '20px' }">在运机组装机容量(MW)</span>
              </div>
            </h2>
            <div ref="chart2Ref" class="mt-[20px] flex justify-center p-[10px]" style="height: 300px">
              <img src="/images/chart7.png" style="object-fit: cover; height:18rem;margin-top:10px;">
            </div>
            <div ref="chart3Ref" class="flex justify-center p-[10px] mt-[-20px]" style="height: 400px">
              <img src="/images/chart8.png" style="object-fit: cover; height:18rem;margin-top:10px;">
            </div>
          </div>
        </div>
      </div>



    </div>
  </div>

</template>

<script lang="ts" setup>
import {
  Search,
} from '@element-plus/icons-vue'
import { reactive } from 'vue'
import { getOperList, getWorldDict, type IOperList, type Operational } from '~/api/crawler/global'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import { CENTERED_ALIGNMENT } from 'element-plus/es/components/virtual-list/src/defaults.mjs'

const { isFullscreen, toggleFullscreen } = useFullscreenLayout()


const searchForm = reactive({
  country: '',
  searchDate: '',
})
const searchForm_gj = reactive({
  status: '1',
} as Operational)
const list_gj = ref<Operational[]>([])

const operList = ref<IOperList[]>([])
const search = () => {
  getOperList(searchForm).then(res => {
    operList.value = res.rows ?? []
    chart(operList.value)
    chart1(operList.value)
    chart2(operList.value)
    chart3(operList.value)
  })

  getWorldDict(searchForm_gj).then(res => {
    list_gj.value = res.data ?? []
  })
}
search()

const chartRef = ref()
const chart = (data: IOperList[]) => {
   const total = data.reduce((sum, item) => sum + item.reactors, 0);
  const option = {
    color: ['#DE66AE', '#EC8ACE', '#E6BBF0', '#9C95F4', '#7F79E7', '#95BEFF', '#95BEFF', '#38A1D8', '#32C2E7', '#64DEDF', '#9EE4BE', '#C4F76B', '#EBF969', '#FCD969', '#FFA66E', '#F6788B', '#64DEDF', '#F6788B'], // 颜色数组
    title: {
      show: false
    },
     tooltip: {
  trigger: 'item',
  textStyle: {fontSize: 20,   },
  formatter: function(params:any) { // 使用回调函数动态格式化
    const value = params.value; // 原始数值
    const name = params.name;   // 数据项名称
    const percent = params.percent; // 自动计算的百分比（需手动格式化）
         // 字体大小（单位px）[4,8](@ref)


           
    // 保留1位小数 + 添加百分号
    const formattedPercent = percent !== null 
      ? percent.toFixed(1) + '%' 
      : 'N/A'; // 异常处理
    
    return `${name}<br/>${value} (${formattedPercent})`;
  }
},
    legend: {
      show: false
    },
    series: [
      {
        name: '机组数量',
        type: 'pie',
        center: ['50%', '30%'],
        radius: ['20%', '40%'],
       data: data.map(t => {
                const percentage = (t.reactors / total) * 100;
                const showLabel = percentage >= 1; // ≥1%才显示
                
                return {
                    value: t.reactors,
                    name: useCountry().getCountryChineseName(t.country),
                    // 动态控制标签和引导线显隐
                    label: {
                        show: showLabel,
                        formatter: function(params:any) {
        return `${params.name}${(params.percent ?? 0).toFixed(1)}%`;},
                        textBorderColor: '#000',
                        textBorderWidth: 0,
                        color: '#222',
                        fontSize: 13
                    },
                    labelLine: {
                        show: showLabel // 同步控制引导线
                    }
                };
            }),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          formatter: '{b}',// 添加百分比显示
          textBorderColor: '#000', // 文字描边颜色
          textBorderWidth: 0,
          color: '#222',
          fontSize: 13
        }

      }
    ]
  }
  const myChart = echarts.init(chartRef.value)
  myChart.setOption(option)
}

const chart1Ref = ref()

const chart1 = (data: IOperList[]) => {
  let newData = data.sort((a, b) => b.reactors - a.reactors)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      textStyle: {fontSize: 20,   },
    },
    grid: {
      left: '2%',
      right: '3%',
      top: '8%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: newData.map(t => useCountry().getCountryChineseName(t.country)),
      axisLabel: {
        fontSize: 14,show: false

      }
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        data: newData.map(t => t.reactors),
        type: 'bar',
        itemStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1, // 垂直渐变（从上到下）
            colorStops: [
              { offset: 0, color: '#FFAA3A' }, // 顶部颜色
              { offset: 1, color: '#FFAA3A' }  // 底部颜色
            ]
          },
          borderRadius: [10, 10, 0, 0] // 左上 右上 右下 左下 的圆角半径
        }
      }
    ]
  };
  const myChart = echarts.init(chart1Ref.value)
  myChart.setOption(option)
}

const chart2Ref = ref()
const chart2 = (data: IOperList[]) => {
   const total = data.reduce((sum, item) => sum + item.capacityTotal, 0);
  const option = {
    color: ['#DE66AE', '#EC8ACE', '#E6BBF0', '#9C95F4', '#7F79E7', '#95BEFF', '#95BEFF', '#38A1D8', '#32C2E7', '#64DEDF', '#9EE4BE', '#C4F76B', '#EBF969', '#FCD969', '#5175FD', '#52AEFF'], // 颜色数组
    title: {
      show: false
    },
    tooltip: {
  trigger: 'item',
  textStyle: {fontSize: 20,   },
  formatter: function(params:any) { // 使用回调函数动态格式化
    const value = params.value; // 原始数值
    const name = params.name;   // 数据项名称
    const percent = params.percent; // 自动计算的百分比（需手动格式化）
    
    // 保留1位小数 + 添加百分号
    const formattedPercent = percent !== null 
      ? percent.toFixed(1) + '%' 
      : 'N/A'; // 异常处理
    
    return `${name}<br/>${value} (${formattedPercent})`;
  }
},
    legend: {
      show: false
    },
    series: [
      {
        name: '装机容量',
        type: 'pie',
        center: ['50%', '30%'],
        radius: ['20%', '38%'],
       data: data.map(t => {
                const percent = (t.capacityTotal / total) * 100;
                const showLabel = percent >= 1; // 仅当≥1%时显示标签
                
                return {
                    value: t.capacityTotal,
                    name: useCountry().getCountryChineseName(t.country),
                    // 动态控制标签和引导线
                     label: {
                        show: showLabel,
                        formatter: function(params:any) {
        return `${params.name}${(params.percent ?? 0).toFixed(1)}%`;},
                        textBorderColor: '#000',
                        textBorderWidth: 0,
                        color: '#222',
                        fontSize: 13
                    },
                    labelLine: {
                        show: showLabel // 同步隐藏引导线
                    }
                };
            }),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
     
      }
    ]
  }
  const myChart = echarts.init(chart2Ref.value)
  myChart.setOption(option)
}

const chart3Ref = ref()
const chart3 = (data: IOperList[]) => {
  let newData = data.sort((a, b) => b.capacityTotal - a.capacityTotal)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      textStyle: {fontSize: 20,   },
    },
    grid: {
      left: '2%',
      right: '3%',
      top: '8%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: newData.map(t => useCountry().getCountryChineseName(t.country)),
      axisLabel: {
        fontSize: 14, show: false
      }
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        data: newData.map(t => t.capacityTotal),
        type: 'bar',
        itemStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1, // 垂直渐变（从上到下）
            colorStops: [
              { offset: 0, color: '#0099FC' }, // 顶部颜色
              { offset: 1, color: '#5AD6F1' }  // 底部颜色
            ]
          },
          borderRadius: [10, 10, 0, 0] // 左上 右上 右下 左下 的圆角半径
        }
      }
    ]
  };
  const myChart = echarts.init(chart3Ref.value)
  myChart.setOption(option)
}

</script>
<style scoped>
.bodybg {
  background: url(/images/zyfw_bg1.png) no-repeat center 76px #edf6ff;
  background-size: cover;
  min-width: 320px;
  /* 改为移动端友好 */
  overflow: hidden;
}
.box {
  transform: scale(0.72);
  transform-origin: 0 0;
}
.topbg_industry {
  background: url(/images/zyfw_topbg.png) no-repeat top;
  background-size: cover;
  height: 55px;
  width: 100%;
}
.backbg {
  background: url(/images/zyfw_back.png) no-repeat top;
  background-size: contain;
  width: 155px;
  height: 26px;
  color: #fff;
  text-align: center;
  font-size: 16px;
  line-height: 27px;
}

.b_l {
  padding-bottom: 12px;
  border-bottom: 3px solid;
  border-image: linear-gradient(to right, #36b0ff, #0080d4) 1;
  font-weight: 500;
}
.left {
  width: 28.5rem;
}
:deep(.el-table) {
  background: rgba(255, 255, 255, 0.3);
}

:deep(.el-table .cell) {
  color: #303133;
}

:deep(.el-table .el-table__cell) {}

:deep(.el-table td.el-table__cell) {
  border-bottom: none;
}

:deep(.el-table th.el-table__cell .cell) {
  background-color: #8bd3f7;
  color: #fff;
  padding: 6px 0;
  padding-left: 25px;
}

:deep(.el-table .cell) {
  color: #212121;
  padding-left: 25px;
  white-space: nowrap;
  word-break: keep-all;
}

:deep(.el-table tr) {
  background-color: rgba(255, 255, 255, 0.5);
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell) {
  background-color: rgba(220, 243, 251, 0.5);
}

.h-20 {
  height: 20rem;
}

.flex-container {
  display: flex;
  /* flex-wrap: wrap; */
  gap: 1rem;
}

.flex-item {
  flex: 1 1 350px;
  /* 最小320px */
  min-width: 350px;
  /* 移动端友好 */
}

.main {
  flex: 2 1 440px;
  min-width: 440px;
  /* 移动端友好 */
}

@media (max-width: 1024px) {
  .main {
    /* order: 0; 中间内容优先显示 */
    flex-basis: 100%;
  }
}

:deep(.demo-form-inline .el-select) {
  --el-select-width: 150px;
}

:deep(.custom-date-picker) {
  width: 150px;
  --el-date-editor-width: 100%;
  /* 覆盖内部变量 */
  height: 26px;
  font-size: 12px;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 10px;
  margin-bottom: 10px;
}

:deep(.el-select__wrapper) {
  min-height: 26px;
  font-size: 12px;
}

.custom-height-btn {
  background: linear-gradient(90deg, #36b0ff 0%, #0080d4 100%);
  height: 26px;
  line-height: 26px;
  /* 确保文字垂直居中 */
  padding: 0 10px;
  /* 调整左右内边距 */
  font-size: 12px;
}

.custom-height-btn:hover {
  background: linear-gradient(90deg, #159af0 0%, #0080d4 100%);
}

.demo-form-inline {
  font-size: 20px;
}

/* 中等屏幕 */

:deep(.el-form-item__label) {
  font-size: 16px;
}

:deep(.custom-height-btn) {
  font-size: 14px;
}

:deep(.el-select__input) {
  font-size: 16px;
}
</style>