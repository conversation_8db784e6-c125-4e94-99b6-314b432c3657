<template>
   <div class="px-[40px] pb-[150px]">
    <div class="pt-[10px] text-[24px] text-[#000]">
      {{ data?.title }}
    </div>
    <div class="my-[20px] border-b border-t border-[#DEDEDE] py-[15px] text-sm text-[#717070]">
      <p class="text-left">
        陈列位置：{{ data?.tag }}：{{ data?.titleSub }}
      </p>
      <div class="mt-[5px] flex justify-between">
        <span>提供单位：{{ data?.source ?? '无' }}</span><span>陈列形式： {{ data?.describes ?? '无' }}</span>
      </div>
    </div>
    <div class="y-axis scrollable-div mb-[20px] " v-html="data?.content">
    </div>
    </div>
</template>

<script setup lang="ts">
import { getArticleById } from '~/api/release';
import { ECOLUMN_CODE } from '~/store/column';


definePageMeta({
  name: ECOLUMN_CODE.SCIENCE_DIGITAL_DETAIL,
  layout: 'column',
  submenuCode: ECOLUMN_CODE.SCIENCE_DIGITAL
})

const router = useRouter();
const route = useRoute();
const id = route.params.id as string
const { data } = await getArticleById(id)
</script>