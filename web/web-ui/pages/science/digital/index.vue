<template>
  <div class="ml-[16px] overflow-hidden pr-[5px]">
    <div class="digital overflow-hidden pb-[80px] mb-[150px]">
      <div class="my-[30px] flex bg-[rgba(255,255,255,0.35)] px-[20px] py-[10px] text-[16px] text-[#001228]">
        <span :class="{ 'hover': active == 0 }"
          class="mx-[15px] rounded-[15px] bg-white px-[15px] py-[5px] text-center transition hover:bg-[#0B80E8] hover:text-white cursor-pointer"
          @click="active = 0">第一篇：扬帆起航</span>
        <span :class="{ 'hover': active == 1 }"
          class="mx-[15px] rounded-[15px] bg-white px-[15px] py-[5px] text-center transition hover:bg-[#0B80E8] hover:text-white cursor-pointer"
          @click="active = 1">第二篇：自立更生</span>
        <span :class="{ 'hover': active == 2 }"
          class="mx-[15px] rounded-[15px] bg-white px-[15px] py-[5px] text-center transition hover:bg-[#0B80E8] hover:text-white cursor-pointer"
          @click="active = 2">第三篇：赶超先进</span>
        <span :class="{ 'hover': active == 3 }"
          class="mx-[15px] rounded-[15px] bg-white px-[15px] py-[5px] text-center transition hover:bg-[#0B80E8] hover:text-white cursor-pointer"
          @click="active = 3">第四篇：再创辉煌</span>
      </div>
      <div class="relative grid grid-cols-3 gap-4 px-[30px]">
        <div v-for="item in getList"
          class=" border border-[#C7E3FF] rounded-[15px] bg-white py-[20px] text-left hover:shadow-[#C7E3FF] hover:shadow-lg">
          <div class="mx-[20px] overflow-hidden text-center">
            <img :src="`${config.public.backendApi}${item?.image}`" class="transition ease-in h-[184px] hover:scale-[1.05]  " style='object-fit:cover'>
          </div>
          <div
            class="mx-[20px] mt-[15px] inline-block rounded-[15px] bg-[#E6F2FF] px-[15px] py-[5px] text-sm text-[#318AD9]">
            {{ item.source }}
          </div>
          <div class="line-clamp-1 mx-[20px] mt-[15px] text-base hover:text-[#575757]">
            <a href="#" class="hover:text-[#229cfc]">{{ item.title }}</a>
          </div>
          <div class="line-clamp-1 mx-[20px] mt-[15px] text-sm text-[#8B8B8B]">
            {{ item.titleSub }}
          </div>
          <div class="mt-[15px] flex justify-between border-t border-[#eee] px-[20px] pt-[15px]">
            <span class="line-clamp-1 text-sm text-[#575757]"><i
                class="iconfont icon-goodsppecategory mr-[5px] text-[#AFAFAF]" />{{ item.describes  ?? '无' }}</span>
            <a class="text-[14px] color-[#469EE4] hover:color-[#229cfc] cursor-pointer" @click="viewDetail(item)">查看详情>></a>
          </div>
        </div>
      </div>
    </div>
  </div>
  <el-dialog v-model="dialogVisible" width="800" class="custom-dialog" :show-close="false">
    <span
      class="iconfont icon-guanbi2 absolute right-[20px] top-[20px] cursor-pointer text-[24px] text-[#717070] hover:text-[#027cd1]"
      @click="dialogVisible = false" />
    <div class="pt-[10px] text-[24px] text-[#000]">
      {{ detail?.title }}
    </div>
    <div class="my-[20px] border-b border-t border-[#DEDEDE] py-[15px] text-sm text-[#717070]">
      <p class="text-left">
        陈列位置：{{ detail?.tag }}：{{ detail?.titleSub }}
      </p>
      <div class="mt-[5px] flex justify-between">
        <span>提供单位：{{ detail?.source ?? '无' }}</span><span>陈列形式： {{ detail?.describes ?? '无' }}</span>
      </div>
    </div>
    <div class="y-axis scrollable-div mb-[20px] " v-html="detail?.content">
    </div>

  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessageBox } from 'element-plus'
import { ECOLUMN_CODE } from '~/store/column'

definePageMeta({
  name: ECOLUMN_CODE.SCIENCE_DIGITAL,
  layout: 'column',
})

const router = useRouter();

const config = useRuntimeConfig()
const active = ref(0)
const list1 = ref<IRelease[]>([])
const list2 = ref<IRelease[]>([])
const list3 = ref<IRelease[]>([])
const list4 = ref<IRelease[]>([])
useReleaseList(ECOLUMN_CODE.SCIENCE_DIGITAL, 1, 1000).then(res => {
  list1.value = res.filter(t => t.tag?.includes('第一篇'))
  list2.value = res.filter(t => t.tag?.includes('第二篇'))
  list3.value = res.filter(t => t.tag?.includes('第三篇'))
  list4.value = res.filter(t => t.tag?.includes('第四篇'))
})

const getList = computed(() => {
  if (active.value == 0) return list1.value
  else if (active.value == 1) return list2.value
  else if (active.value == 2) return list3.value
  else return list4.value
})

const detail = ref<IRelease>()
const viewDetail = (item: IRelease) => {
    router.push({
        name: ECOLUMN_CODE.SCIENCE_DIGITAL_DETAIL,
        params: {id: item.id}
    })
//   dialogVisible.value = true
//   detail.value = item
}

const dialogVisible = ref(false)

</script>

<style scoped>
.digital {
  background: url(/images/kepu-szgb.png) no-repeat #fafcff;
  
}

.neiye {
  padding-bottom: 10px;
}

.hover {
  background-color: #0b80e8;
  color: #fff;
}

.arrow_l {
  position: absolute;
  width: 30px;
  height: 30px;
  top: calc(50% - 30px / 2 + 10.5px);
  background: linear-gradient(270deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.1) 100%);
  opacity: 0.8;
  border-radius: 20px;
  transform: matrix(-1, 0, 0, 1, 0, 0);
  color: #fff;
  line-height: 30px;
  cursor: pointer;
}

.arrow_l:hover {
  background: linear-gradient(270deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.3) 100%);
}

/* 定义滚动条整体样式 */
.y-axis::-webkit-scrollbar {
  width: 6px;
  /* 设置滚动条宽度 */
}

/* 定义滚动条滑块样式 */
.y-axis::-webkit-scrollbar-thumb {
  background-color: #cacaca;
  /* 设置滑块颜色 */
  border-radius: 5px;
  /* 设置滑块圆角 */
}

/* 定义滚动条轨道样式 */
.y-axis::-webkit-scrollbar-track {
  background: #e5eefb;
  /* 设置轨道颜色 */
}

/* 兼容非Webkit浏览器的样式 */
.y-axis {
  -ms-overflow-style: scrollbar;
  /* IE和Edge */
  scrollbar-width: thin;
  /* Firefox */
  scrollbar-color: #cacaca #fff;
  /* Firefox */
}

.scrollable-div {
  height: 440px;
  overflow-y: auto;
  /* 内容超出时显示滚动条 */
  scroll-behavior: smooth;
  /* 平滑滚动 */
  line-height: 30px;

}

.scrollable-div img {
  text-align: center;
}
</style>
<style>
.custom-dialog {
  background: url(/images/kepu-digital-bg.png) #fff;
}
img{display:inline-block!important;}
</style>