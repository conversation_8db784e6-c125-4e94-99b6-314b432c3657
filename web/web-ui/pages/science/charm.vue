<template>
  <div class="ml-[16px] overflow-hidden pr-[5px]">
    <div class="charm">
      <div class="flex flex-col items-end pl-[50px] pr-[50px] pt-[50px]">
        <img src="/images/kepu-font.png" width="587px" height="81px">
        <span class="mr-[20px] text-[36px] color-[#37B5BF]">核科普知识竞赛</span>
        <a class="ml-[240px] mr-[20px] mt-[30px] leading-[35px] btn hover:text-[#d8fffe]"
          href="http://mlzg.hy.nuclearmarket.cn/" target="_blank">
          点击进入
        </a>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

definePageMeta({
  layout: 'column',

})
</script>
<style scoped>
.charm {
  background: url(/images/kepu-mlzg.png) no-repeat;
 
  height: 648px; 
}

.neiye {
  padding-bottom: 10px;
}

.btn {
  background: url(/images/kepu-btn.png);
  width: 150px;
  height: 45px;
}
</style>