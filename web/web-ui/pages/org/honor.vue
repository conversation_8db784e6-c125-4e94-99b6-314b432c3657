<template>
    <div class="ml-[16px] overflow-hidden pr-[12px] mb-[100px] min-h-[700px]">
        <div class="banner">
            <img src="/images/banner_ry.png">
        </div>
        <div class="px-[20px] py-[20px]">
            <div class="text-left">
                <div v-for="(item, index) in list" :class="{ '!bg-[#fbfbfb]': index % 2 === 1 }"
                    class="mb-[24px] flex  bg-white p-[24px] shadow transition ease-in hover hover:bg-[#f3f9ff] justify-between hover:shadow-lg">
                    <div class="mr-[20px] w-[70%]">
                        <h1 class="mt-1">
                            <a class="line-clamp-1 w-[80%] text-lg">{{ item.title }}</a>
                        </h1>
                        <div class="mt-2 text-gray-500 leading-6">
                            <div class="line-clamp-2">
                                {{ item.describes }}
                            </div>
                        </div>

                        <div class="mt-[8px] text-xs text-gray-400">
                            <span class="iconfont icon-shijian" /> {{ dateFormat(item.date) }}
                        </div>
                    </div>
                    <div class="overflow-hidden">
                        <el-popover placement="left" width="auto">
                            <template #reference>
                                <el-image ref="imageRef" style="width: 100px; height: 100px"
                                    :src="`${config.public.backendApi}${item?.img}`" show-progress fit="cover">
                                    <template #error>
                                        <div class="el-image__error">无图片</div>
                                    </template>
                                </el-image>
                            </template>
                            <template #default>
                                <el-image ref="imageRef" style="width: 450px;height: auto;"
                                    :src="`${config.public.backendApi}${item?.img}`" :preview-src-list="[`${config.public.backendApi}${item?.img}`]" show-progress fit="cover">
                                </el-image>
                            </template>
                        </el-popover>

                    </div>
                </div>

                <div class="flex justify-center">
                    <the-pagination :total="total" v-model:page="searchForm.pageNum" v-model:limit="searchForm.pageSize"
                        @pagination="search" layout="prev, pager, next"></the-pagination>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { template } from 'lodash';
import { ECOLUMN_CODE } from '~/store/column';

definePageMeta({
    name: ECOLUMN_CODE.ORG_HONOR,
    layout: 'column',

})

const config = useRuntimeConfig()
interface IHonor {
    id?: string
    /** 标题 */
    title?: string
    /** 描述 */
    describes?: string
    /** 照片 */
    img?: string
    /** 日期 */
    date?: string
}
const total = ref(0)
const list = ref<IHonor[]>([])
const searchForm = reactive({
    pageNum: 1,
    pageSize: 10,
})
const search = () => {
    useReleasePage(ECOLUMN_CODE.ORG_HONOR, searchForm.pageNum, searchForm.pageSize).then(res => {
        total.value = res.total ?? 0
        list.value = res.rows?.map(item => {
            return {
                id: item.id,
                title: item.title,
                img: item.image,
                describes: item.describes,
                date: item.articleDate
            }
        }) ?? []
    })
}
search()
</script>
<style scoped>
.hover img {
    transition: all ease 0.4s;
}

.hover:hover img {
    scale: 1.5;
}
</style>