<template>
    <div class="ml-[16px] pb-10 mb-[150px] pr-[12px]">
        <div class="banner">
            <img src="/images/banner_ld.png">
        </div>
        <div class="ldbg flex flex-col items-center ">

            <div class="flex flex-wrap mt-[60px] justify-center">
                <div v-if="list.at(0)"
                    class="ldbg-card h-[228px] w-[430px]  border-t-2 border-[#FF810E] rounded-[15px] shadow shadow-[#9bd5f2] hover:shadow-[#B7CDE7] hover:shadow-lg">
                    <div class="flex p-30px py-20px">
                        <div class="tx h-[100px] w-[100px] overflow-hidden rounded-full">
                            <img :src="list.at(0)?.img ? `${config.public.backendApi}${list.at(0)?.img}` : '/images/ld_tx.png'"
                                style="width:100%; height:100px;object-fit:cover">
                        </div>
                        <div class="ml-[15px] flex-1 text-left">
                            <h1 class="mt-[20px] border-b border-[#B1DCFF] pb-[10px] text-[20px]">
                                {{ list.at(0)?.name }}
                            </h1>
                            <p class="mt-[15px] text-[15px] color-[#434343]">
                                <span class="iconfont icon-zhuanjizhicheng pr-[3px] text-[#318AD9]" />{{
                                list.at(0)?.postion }}
                            </p>
                            <!-- <p class="mt-[5px] text-[15px] color-[#434343]">
                                <span class="iconfont icon-jisuanqi pr-[3px] text-[#318AD9]" />{{ list.at(0)?.contact }}
                            </p> -->
                            <div class="mt-[20px] w-[88px] cursor-pointer border border-[#469BDE] border-solid rounded-[3px] text-center text-[13px] text-[#318AD9] leading-[28px] transition ease-in-out hover:bg-[#318AD9] hover:text-white"
                                @click="$router.push({ name: ECOLUMN_CODE.ORG_LEADER_DETAIL, params: { id: list.at(0)?.id } })">
                                查看详情>>
                            </div>
                        </div>
                    </div>
                </div>
            
            </div>
            <div class="flex flex-wrap mt-[30px] justify-center">
                 <div v-for="item in list.slice(1)"
                    class="ldbg-card h-[228px] w-[430px] mb-[20px] mx-[10px] border-t-2 border-[#318AD9] rounded-[15px] shadow shadow-[#9bd5f2] hover:shadow-[#B7CDE7] hover:shadow-lg">
                    <div class="flex p-30px py-20px">
                        <div class="tx h-[100px] w-[100px] overflow-hidden rounded-full">
                            <img :src="item?.img ? `${config.public.backendApi}${item?.img}` : '/images/ld_tx.png'"
                                style="width:100%; height:100px;object-fit:cover">
                        </div>
                        <div class="ml-[15px] flex-1 text-left">
                            <h1 class="mt-[20px] border-b border-[#B1DCFF] pb-[10px] text-[20px] ">
                                {{ item.name }}
                            </h1>
                            <p class="mt-[15px] text-[15px] color-[#434343] mr-[10px]">
                                <span class="iconfont icon-zhuanjizhicheng pr-[3px] text-[#318AD9] " />{{ item?.postion
                                }}
                            </p>
                            <!-- <p class="mt-[5px] text-[15px] color-[#434343]">
                                <span class="iconfont icon-jisuanqi pr-[3px] text-[#318AD9]" />{{ item?.contact }}
                            </p> -->
                            <div class="mt-[20px] w-[88px] cursor-pointer border border-[#469BDE] border-solid rounded-[3px] text-center text-[13px] text-[#318AD9] leading-[28px] transition ease-in-out hover:bg-[#318AD9] hover:text-white"
                                @click="$router.push({ name: ECOLUMN_CODE.ORG_LEADER_DETAIL, params: { id: item.id } })">
                                查看详情>>
                            </div>
                        </div>
                    </div>
                </div>
              
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ECOLUMN_CODE } from '~/store/column';

definePageMeta({
    name: ECOLUMN_CODE.ORG_LEADER,
    layout: 'column',
})

const config = useRuntimeConfig()
const list = ref<ILeader[]>([])
useReleaseList(ECOLUMN_CODE.ORG_LEADER, 1, 100).then(res => {
    list.value = res.map(item => {
        return {
            id: item.id,
            name: item.title,
            img: item.image,
            postion: item.describes,
            contact: item.source,
            content: item.content,
        }

    })
})
</script>

<style scoped>
.ldbg {
    background: linear-gradient(180deg, #eff8ff 0%, rgba(255,255,255,0.4) 100%);
}

.ldbg-card {
    background: url(/images/ld_bg.png) no-repeat bottom #fff;
}

.tx {
    box-shadow: 0px 4px 4px rgba(84, 163, 226, 0.5);
}
</style>
