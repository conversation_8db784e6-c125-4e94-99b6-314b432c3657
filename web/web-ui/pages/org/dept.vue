<template>
    <div class="ml-[16px] overflow-hidden pr-[12px] mb-[200px]">
        <div class="banner">
            <img src="/images/banner_sz.png">
        </div>
        <div class="px-[20px] py-[20px]">
            <div v-for="(item, index) in depts" :key="item.id"
                class="set flex transition hover:shadow-[#afd1fb] hover:shadow-lg">
                <div class="set1 h-[110px] w-[120px] flex flex-col items-center justify-center text-white">
                    <!-- <span
                        class="din_font mt-[15px] h-[50px] w-[50px] rounded-full bg-white text-[24px] text-[#2894F5] leading-[50px]">{{
                        index + 1 }}</span> -->
                    <span class="mt-[4px] text-[20px] font-medium">{{ item.dept }}</span>
                </div>
                <div class="ml-[20px] flex-1 text-left">
                    <p class="text-[17px] leading-[30px]">
                        {{ item.duty }}
                    </p>
                    <div class="mt-[16px] text-[#318AD9]">
                        <span><em class="iconfont icon-yonghu" />{{ item.principal }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ECOLUMN_CODE, useColumnStore } from '~/store/column'


definePageMeta({
    name: ECOLUMN_CODE.ORG_DEPT,
    layout: 'column',
})

interface IDept {
    id?: string
    /** 部门 */
    dept?: string
    /** 职责 */
    duty?: string
    /** 负责人 */
    principal?: string
    /** 联系方式 */
    contact?: string
}

const depts = ref<Array<IDept>>([])
useReleaseList(ECOLUMN_CODE.ORG_DEPT, 1, 100).then(res => {
    depts.value = res.map(item => {
        return {
            id: item.id,
            dept: item.title,
            duty: item.describes,
            principal: item.titleSub,
            contact: item.source,
        }
    })
})
</script>

<style scoped>
.set {
    @apply p-[30px] mt-[24px];
    background: url(/images/sz_bg1.png) no-repeat bottom #fdfdfd;
    border: solid 1px #ddf0ff;
}

.set1 {
    position: relative;
    background: url(/images/sz_bg2.png) no-repeat center;
}
</style>