<template>
  <div id="printSection" class="ml-[16px] mb-[100px] pr-[12px]">
    <div class="banner">
      <img src="/images/banner_jg.png">
    </div>
    <TheZoom >
      <div v-html="content"></div>
      <ThePrintTool target-area-id="printSection" :show-close="false" />
    </TheZoom>

  </div>
</template>

<script setup lang="ts">
import { ECOLUMN_CODE, useColumnStore } from '~/store/column'


definePageMeta({
  name: ECOLUMN_CODE.ORG_ABOUT,
  layout: 'column',

})

const content = ref('')
useReleaseList(ECOLUMN_CODE.ORG_ABOUT, 1, 1).then(res => {
  content.value = res.at(0)?.content || ''
})

</script>

<style scoped>
@media print {
  .no-print {
    display: none;
  }
}
</style>