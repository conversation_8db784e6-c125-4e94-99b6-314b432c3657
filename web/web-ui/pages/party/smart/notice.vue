<template>
  <div class="mt-[15px] px-[20px] text-left mb-[100px]">
    <div v-for="item in list"
      class="relative flex justify-between border-b border-[#eee] px-[5px] py-[12px] transition ease-in hover:bg-[#FFF6F6]"
      @click="$router.push({ name: 'RELEASE', params: { id: item.id, type: 'party-smart' } })">
      <span class="absolute top-[21px] h-[5px] w-[5px] bg-[#EC3A3A]" />
      <h1 class="ml-[15px] w-[70%]">
        <a class="line-clamp-1 cursor-pointer text-[16px] hover:text-[#EC3A3A]" :title="item.title">{{ item.title }}</a>
      </h1>
      <div class="text-sm  w-[110px]">
        <span class="ml-[4px] text-gray-400"><i class="iconfont icon-shijian" />{{ dateFormat(item.articleDate)
          }}</span>
      </div>
    </div>
    <div class="mt-[20px] flex justify-center">
      <the-pagination 
            :total="total" 
            v-model:page="searchForm.pageNum" 
            v-model:limit="searchForm.pageSize"
            @pagination="handlePaginationChange" layout="prev, pager, next" 
          ></the-pagination>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onActivated } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ECOLUMN_CODE } from '~/store/column';

definePageMeta({
    name:ECOLUMN_CODE.PARTY_SMART_NOTICE,
    layout: 'column-party',

})


const route = useRoute();
const router = useRouter();

const total = ref(0);
const list = ref<IRelease[]>([]);

const searchForm = reactive({
  pageNum: Number(route.query.page) || 1, // 从路由获取页码
  pageSize: 10,
});

// 跳转到详情页
const toDetail = (id: any) => {
  router.push({
    name: 'RELEASE',
    params: { id, type: 'column-party' },
    query: { 
      originPage: searchForm.pageNum, // 传递当前页码
      originType: 'news' 
    }
  });
};



// 处理分页变化
const handlePaginationChange = () => {
  // 更新URL中的页码参数
  router.replace({ 
    query: { ...route.query, page: searchForm.pageNum } 
  });
  search();
};

// 获取数据

const search = () => {
  useReleasePage(ECOLUMN_CODE.PARTY_SMART_NOTICE, searchForm.pageNum, searchForm.pageSize).then(res => {
    total.value = res.total ?? 0
    list.value = res.rows ?? []
  })
}
// 从路由恢复分页状态
onMounted(() => {
  if (route.query.page) {
    searchForm.pageNum = Number(route.query.page);
  }
  search();
});

// 从详情页返回时恢复状态
onActivated(() => {
  if (route.query.page && Number(route.query.page) !== searchForm.pageNum) {
    searchForm.pageNum = Number(route.query.page);
    search();
  }
});

// 日期格式化函数
// const dateFormat = (date: string) => {
//   return new Date(date).toLocaleDateString();
// };
</script>
<style scoped>

.shadow {
    box-shadow: 0px 4px 4px rgba(255, 233, 233, 0.25);
}

:deep(.el-pagination.is-background .el-pager li.is-active) {
    background-color: #ec3a3a;
}
</style>