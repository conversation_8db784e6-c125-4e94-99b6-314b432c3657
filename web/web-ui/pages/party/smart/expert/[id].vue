<template>

    <div class="m-[20px] bg-white p-[20px] text-left text-[18px]">
        <div class="bg-[#FFF7F7] border-1 border-[#FFD1D3] inline-block px-4 rounded cursor-pointer"
            @click="$router.back()">
            <span class="text-[#EC3A3A] text-3.5">返回</span>
        </div>
        <div class="relative bg-[#FFEEEE] py-[8px] pl-[24px] text-[16px] text-[#444] mt-2">
            <span
                class="absolute left-[13px] top-[13px] inline-block h-[15px] w-[4px] rounded-[10px] bg-[#CD4545]" />{{ detail?.fl }}（{{
            detail?.zz }}
           <span class="ml-[15px]">{{ detail?.fz }}）</span> 
        </div>
        <el-table :data="tableList" style="width: 100%">
            <el-table-column prop="序号" label="序号" width="70" />
            <el-table-column prop="姓名" label="姓名" width="90" />
            <el-table-column prop="性别" label="性别" width="70" />
            <el-table-column prop="单位" label="单位"  min-width="210"/>
            <el-table-column prop="职务" label="职务" min-width="230">
                    <template #default="{ row }">
                        <div v-if="Array.isArray(row.职务)">
                            <div v-for="(job, index) in row.职务" :key="index" class="job-item">
                                {{ job }}
                            </div>
                        </div>
                        <span v-else>{{ row.职务 }}</span>
                    </template>
                </el-table-column>
        </el-table>
    </div>
</template>

<script setup lang="ts">
import { getArticleById } from '~/api/release';
import { ECOLUMN_CODE } from '~/store/column';
import { parse } from 'csv-parse/browser/esm/sync'

definePageMeta({
    name: ECOLUMN_CODE.PARTY_SMART_EXPERT_DETAIL,
    layout: 'column-party',
    submenuCode: ECOLUMN_CODE.PARTY_SMART
})

const id = useRoute().params.id as string;
const detail = ref<IExpert>()
const tableList = ref<any[]>([])
getArticleById(id).then(res => {
    detail.value = res.data ? [res.data].map(item => {
        return {
            id: item.id,
            zz: item.title,
            fz: item.titleSub,
            fl: item.tag,
        }
    }).at(0) : undefined
    tableList.value = parse(res.data?.describes??'', {
        columns: true,
        skip_empty_lines: true,
        delimiter: ','
    }).map((item:any, index:any) => {
        const a = item.职务.split('、')
        return {
            ...item,
            职务: a
        }
    })
})

</script>
