<template>
    <div class="mt-[30px] px-[20px] text-left text-[18px] mb-[100px]">
        <div v-if="zr" class="experts-top flex py-[10px] text-[18px]">
            <span class="ml-[20px]"><i class="iconfont icon-yonghu text-normal color-[#CD4545]" /> {{ zr?.zz }}</span><span
                class="ml-[20px]"> <i class="iconfont icon-yonghu text-normal color-[#CD4545]" /> {{ zr?.fz }}</span>
        </div>
        <div class="grid grid-cols-3 mt-[20px] gap-3">
            <div v-for="(item, index) in list" class="experts relative mb-[20px] h-[230px] shadow shadow-[#9A4141]/15">
                <div class="mb-[10px] flex flex-col items-center pl-[20px] pt-[10px] text-[13px] color-[#6c6c6c]">
                    <div
                        class="din_font mt-[20px] h-[48px] w-[48px] bg-[#EC3A3A] text-center text-[26px] text-white leading-[48px]">
                        {{ index + 1 }}
                    </div>
                    <div class="mt-[10px] text-[20px] text-[#C70000]">
                        {{ item.fl }}
                    </div>
                    <div class="mt-[5px] text-[16px] color-[#444] line-clamp-1 mr-[10px]">
                        <span>{{ item.zz }}</span><span class="ml-[10px]">{{ item.fz }}</span>
                    </div>
                    <div class="mx-[30px] mt-[15px] w-[200px] cursor-pointer border border-[#FFD1D3] rounded-[5px] bg-[rgba(253,235,227,0.5)] text-center text-[14px] text-[#FD3737] leading-[36px] transition ease-in-out hover:bg-white"
                        @click="$router.push({ name: ECOLUMN_CODE.PARTY_SMART_EXPERT_DETAIL, params: { id: item.id } })">
                        详情
                    </div>
                </div>
                <div
                    class="first absolute bottom-[-12px] z-[-1] h-[12px] w-[90%] rounded-b-[10px] bg-[rgba(253,218,218,1)]" />
                <div
                    class="first absolute bottom-[-23px] z-[-2] h-[24px] w-[80%] rounded-b-[10px] bg-[rgba(255,242,242,0.9)]" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ECOLUMN_CODE } from '~/store/column';


definePageMeta({
    name: ECOLUMN_CODE.PARTY_SMART_EXPERT,
    layout: 'column-party',

})



const zr = ref<IExpert>()
const list = ref<IExpert[]>([])
useReleaseList(ECOLUMN_CODE.PARTY_SMART_EXPERT, 1, 1000).then(res => {
    zr.value = res.filter(item => item.tag === '主任').slice(0, 1).map(item => {
        return {
            id: item.id,
            zz: item.title,
            fz: item.titleSub,
            fl: item.tag,
        }
    }).at(0)
    list.value = res.filter(item => item.tag != '主任').map(item => {
        return {
            id: item.id,
            zz: item.title,
            fz: item.titleSub,
            fl: item.tag,
        }
    })

})
</script>

<style scoped>
.shadow {
    box-shadow: 0px 0px 4px rgba(154, 65, 65, 0.15);
    border-radius: 8px;
    border: solid 3px #fff;
    transition: all 0.4s ease;
}

.shadow:hover {
    box-shadow: 0px 0px 10px rgba(255, 215, 215, 0.8);
}

:deep(.el-pagination.is-background .el-pager li.is-active) {
    background-color: #ec3a3a;
}

.first {
    left: 50%;
    transform: translate(-50%);
}

.experts-top {
    background: rgba(255, 255, 255, 0.3);
    border: 1px solid #ffc6c9;
    backdrop-filter: blur(2px);
}
</style>
