<template>
  <div class="px-[20px] text-left text-[18px] mb-[100px]">
    <div class="advancedbg arial_font mt-[20px] px-[20px] py-[10px] text-[#F4000C] overflow-hidden flex items-center w-full">
      <div class="scroll-container w-full" ref="scrollContainer">
        <div 
          class="scroll-arrow left" 
          :class="{ hidden: !showLeftArrow }"
          @click="scrollLeft">
          &lt;
        </div>
        <div 
          class="year" 
          ref="yearContainer"
          :style="{ transform: `translateX(${-scrollPosition}px)` }">
          <span 
            v-for="(year, index) in years" 
            :key="year"
            :class="{ 'hover text-white bg-[#EC3A3A]': activeYear === year }"
            @click="setActiveYear(year)">
            {{ year }}
          </span>
        </div>
        <div 
          class="scroll-arrow right" 
          :class="{ hidden: !showRightArrow }"
          @click="scrollRight">
          &gt;
        </div>
      </div>
    </div>

    <div>
      <div v-for="item in list" 
           class="relative flex justify-between border-b border-[#eee] px-[5px] py-[16px] transition ease-in hover:bg-[#FFF6F6]"
           @click="toDetail(item.id)">
        <span class="absolute top-[27px] h-[5px] w-[5px] bg-[#EC3A3A]" />
        <h1 class="ml-[15px] w-[70%]">
          <a class="line-clamp-1 cursor-pointer text-lg hover:text-[#EC3A3A]" :title="item.title">{{ item.title }}</a>
        </h1>
        <div class="mt-[5px] text-sm w-[110px]">
          <span class="ml-[4px] text-gray-400">
            <i class="iconfont icon-shijian" />{{ dateFormat(item.articleDate) }}
          </span>
        </div>
      </div>
    </div>
    <div class="mt-[20px] flex justify-center">
      <the-pagination 
        :total="total" 
        v-model:page="searchForm.pageNum" 
        v-model:limit="searchForm.pageSize" 
        :current-page="searchForm.pageNum"
        @pagination="handlePagination"
        layout="prev, pager, next"
      ></the-pagination>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import { ref, reactive, onMounted, onActivated, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getContentReleaseYearsInterval } from '~/api/release';
import { getColumnIdByCode } from '~/composables/release';
import { ECOLUMN_CODE } from '~/store/column';

definePageMeta({
  name: ECOLUMN_CODE.PARTY_UNION_TYPICAL,
  layout: 'column-party',
  keepAlive: true // 启用组件缓存
})

const route = useRoute();
const router = useRouter();

// 响应式数据
const years = ref<number[]>([]);
const activeYear = ref<number>(dayjs().year());
const scrollPosition = ref(0);
const showLeftArrow = ref(false);
const showRightArrow = ref(false);
const yearContainer = ref<HTMLDivElement | null>(null);
const scrollContainer = ref<HTMLElement | null>(null);
const containerWidth = ref(0);
const contentWidth = ref(0);
const total = ref(0);
const list = ref<IRelease[]>([]);
const searchForm = reactive({
  pageNum: Number(route.query.page) || 1,
  pageSize: 15,
});

// 设置当前激活年份
const setActiveYear = (year: number) => {
  searchForm.pageNum = 1;
  activeYear.value = year;
  updateRouteParams(); // 更新路由参数
};

// 更新路由参数（保存状态到URL）
const updateRouteParams = () => {
  router.replace({
    query: {
      ...route.query,
      year: activeYear.value.toString(),
      page: searchForm.pageNum.toString()
    }
  });
};

// 从路由参数恢复状态
const restoreStateFromRoute = () => {
  if (route.query.year) {
    activeYear.value = Number(route.query.year);
  }
  if (route.query.page) {
    searchForm.pageNum = Number(route.query.page);
  }
};

// 跳转到详情页
const toDetail = (id: any) => {
  // 保存当前状态到URL
  updateRouteParams();
  
  router.push({
    name: 'RELEASE',
    params: { 
      id, 
      type: 'party-union' 
    },
    query: { 
      originPage: searchForm.pageNum.toString(),
      originYear: activeYear.value.toString()
    }
  });
};

// 处理分页变化
const handlePagination = () => {
  updateRouteParams(); // 更新路由参数
  search();
};

// 计算滚动状态
const updateScrollState = () => {
  if (scrollContainer.value && yearContainer.value) {
    containerWidth.value = scrollContainer.value.clientWidth;
    contentWidth.value = yearContainer.value.scrollWidth;
    showLeftArrow.value = scrollPosition.value > 0;
    showRightArrow.value = scrollPosition.value < contentWidth.value - containerWidth.value;
  }
};

// 向左滚动
const scrollLeft = () => {
  const itemWidth = 160;
  scrollPosition.value = Math.max(scrollPosition.value - itemWidth, 0);
  updateScrollState();
};

// 向右滚动
const scrollRight = () => {
  const itemWidth = 160;
  const maxScroll = contentWidth.value - containerWidth.value;
  scrollPosition.value = Math.min(scrollPosition.value + itemWidth, maxScroll);
  updateScrollState();
};

// 日期格式化
const dateFormat = (date: string | Date, format = 'YYYY-MM-DD') => {
  return dayjs(date).format(format);
};

// 处理年份数据
const processYearData = (yearData: string): number[] => {
  if (!yearData) return [];
  
  return yearData.split(',')
    .map(y => parseInt(y.trim()))
    .filter(y => !isNaN(y) && y > 0)
    .sort((a, b) => a - b);
};

// 获取年份数据
const fetchYears = async () => {
  try {
    const res = await getContentReleaseYearsInterval({
      columnId: getColumnIdByCode(ECOLUMN_CODE.PARTY_UNION_TYPICAL),
      sortKind: 'article_date',
      sortAd: 'desc'
    });
    
    years.value = processYearData(res.msg);
    
    if (years.value.length > 0) {
      // 尝试从路由参数恢复年份状态
      if (route.query.year) {
        const routeYear = Number(route.query.year);
        if (years.value.includes(routeYear)) {
          activeYear.value = routeYear;
        } else {
          activeYear.value = years.value[0];
        }
      } else {
        activeYear.value = years.value[0];
      }
    } else {
      activeYear.value = new Date().getFullYear();
    }
  } catch (err) {
    console.error("获取年份失败", err);
  } finally {
    updateScrollState();
  }
};

// 获取文章列表数据
const search = async () => {
  try {
    const res = await useReleasePageByYear(
      ECOLUMN_CODE.PARTY_UNION_TYPICAL, 
      activeYear.value, 
      searchForm.pageNum, 
      searchForm.pageSize
    );
    total.value = res.total ?? 0;
    list.value = res.rows ?? [];
  } catch (err) {
    console.error('获取文章列表失败:', err);
  }
};

// 初始化
onMounted(async () => {
  await fetchYears();
  restoreStateFromRoute();
  await search();
  window.addEventListener('resize', updateScrollState);
});

// 激活时恢复状态（从详情页返回时触发）
onActivated(() => {
  restoreStateFromRoute();
  search();
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', updateScrollState);
});
// 监听年份变化
watch(activeYear, () => {
    search();
});
</script>

<style scoped>
:deep(.el-pagination.is-background .el-pager li.is-active) {
  background-color: #ec3a3a;
}

.scroll-container {
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.year {
  display: flex;
  flex-wrap: nowrap;
  transition: transform 0.4s ease;
  padding: 0 40px;
}

.year span {
  flex: 0 0 auto;
  margin: 0 10px;
  width: 140px;
  font-size: 26px;
  text-align: center;
  padding: 5px 0;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  cursor: pointer;
  border-radius: 4px;
}

.year span:hover,
.year span.hover {
  background-color: #ec3a3a;
  color: #fff;
  margin: 0 10px;
  z-index: 2;
}

.scroll-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
  transition: all 0.3s ease;
  font-weight: bold;
  font-size: 20px;
}

.scroll-arrow:hover {
  background-color: #ec3a3a;
  color: white;
  transform: translateY(-50%) scale(1.1);
}

.scroll-arrow.left {
  left: 0;
}

.scroll-arrow.right {
  right: 0;
}

.scroll-arrow.hidden {
  opacity: 0;
  pointer-events: none;
}
</style>