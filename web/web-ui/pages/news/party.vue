<template>
  <div class="ml-[16px] overflow-hidden pr-[12px] mb-[150px]">
    <div class="banner">
      <img src="/images/banner_news_party.png">
    </div>

    <div class="px-[20px] py-[20px]">
      <div class="text-left">
        <div v-for="item in partyList"
          class="relative flex justify-between border-b border-[#eee] px-[5px] py-[12px] transition ease-in hover:bg-[#f3f9ff]"
          @click="toDetail(item.id)">
          <span class="absolute top-[21px] h-[5px] w-[5px] bg-[#4792CD]" />
          <h1 class="ml-[15px] w-[70%]">
            <a class="line-clamp-1 cursor-pointer text-[16px]" :title="item.title">{{ item.title }}</a>
          </h1>
          <div class="text-sm  w-[110px]">
            <span class="ml-[4px] text-gray-400">
              <i class="iconfont icon-shijian" /> {{ dateFormat(item.articleDate) }}
            </span>
          </div>
        </div>
        <div class="mt-[20px] flex justify-center">
          <the-pagination 
            :total="total" layout="prev, pager, next" 
            v-model:page="searchForm.pageNum" 
            v-model:limit="searchForm.pageSize"
            @pagination="handlePaginationChange"
          ></the-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onActivated } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { ECOLUMN_CODE } from '~/store/column';

definePageMeta({
  name: ECOLUMN_CODE.NEWS_PARTY,
  layout: 'column',
  keepAlive: true // 启用缓存[8](@ref)
})

const route = useRoute();
const router = useRouter();


const total = ref(0);
const partyList = ref<IRelease[]>([]);
const searchForm = reactive({
  pageNum: Number(route.query.page) || 1,
  pageSize: 10,
});


// 跳转到详情页
const toDetail = (id: any) => {
  router.push({
    name: 'RELEASE',
    params: { id, type: 'party' },
    query: { 
      originPage: searchForm.pageNum, // 传递当前页码
      originType: 'news' 
    }
  });
};


// 处理分页变化
const handlePaginationChange = () => {
  // 更新URL中的页码参数
  router.replace({ 
    query: { ...route.query, page: searchForm.pageNum } 
  });
  search();
};



// 获取数据
const search = () => {
  useReleasePage(
    ECOLUMN_CODE.NEWS_PARTY, 
    searchForm.pageNum, 
    searchForm.pageSize
  ).then(res => {
    total.value = res.total ?? 0;
    partyList.value = res.rows ?? [];
  });
};


// 从路由恢复分页状态
onMounted(() => {
  if (route.query.page) {
    searchForm.pageNum = Number(route.query.page);
  }
  search();
});

// 从详情页返回时恢复状态[8](@ref)
onActivated(() => {
  if (route.query.page && Number(route.query.page) !== searchForm.pageNum) {
    searchForm.pageNum = Number(route.query.page);
    search();
  }
});








</script>