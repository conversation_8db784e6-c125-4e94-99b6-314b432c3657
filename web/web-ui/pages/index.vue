<template>
    <div style="position: relative;">
        <div class="grid grid-cols-[340px_1fr_230px] gap-7.5 p-3 p-0">
            <div class="flex flex-col gap-3">
                <div>
                    <div class="flex items-center justify-between pb-2 border_b">
                        <span class="text-[#397fb7] text-5 font-bold">机构职能</span>
                        <span class="text-[#428BC5] text-3.5 cursor-pointer  transition hover:text-[#449bdf]"
                            @click="$router.push({ path: '/org/about' })">更多>></span>
                    </div>
                    <div class="grid grid-cols-[157px_1fr] gap-1 pt-3">
                        <img class="h-[201px] w-[157px]" :src="`${config.public.backendApi}${about?.image}`">
                        <div class="org-info line-clamp-8 text-justify">
                            {{ getHtmlContent(about?.content) }}
                        </div>
                    </div>
                </div>
                <div>
                    <div class="flex items-center justify-between pb-2 border_b">
                        <span class="text-[#397fb7] text-5 font-bold">系统党建</span>
                        <span class="text-[#428BC5] text-3.5 cursor-pointer  transition hover:text-[#449bdf]"
                            @click="$router.push({ path: '/party/union' })">更多>></span>
                    </div>
                    <div class="flex flex-col gap-3 pt-3">
                        <img src="/images/sy_link4.png" class="h-[134px] w-[340px] cursor-pointer"
                            @click="$router.push({ path: '/party/union/about' })">
                        <img src="/images/sy_link5.png" class="h-[132px] w-[340px] cursor-pointer"
                            @click="$router.push({ path: '/party/smart/rule' })">
                    </div>
                </div>
            </div>
            <div class="flex flex-col gap-3">
                <index-the-news></index-the-news>
                <index-the-notice></index-the-notice>
                <index-the-industry></index-the-industry>
            </div>
            <div class="flex flex-col gap-3">
                <div>
                    <div class="flex items-center justify-between pb-2 border_b cursor-pointer">
                        <span class="text-[#397fb7] text-5 font-bold">企业中心</span>
                        <span class="text-[#428BC5] text-3.5  cursor-pointer  transition hover:text-[#449bdf]"
                            @click="$router.push({ name: ECOLUMN_CODE.COMPANY_SERVICE_TRAIN, })">更多>></span>
                    </div>
                    <div class="grid grid-cols-2 gap-4 text-sm pt-3 cursor-pointer">
                        <div class="flex flex-col justify-center items-center qyzx-border py-3"
                            @click="$router.push({ name: ECOLUMN_CODE.COMPANY_SERVICE_TRAIN, })">
                            <img class="w-12.5 h-12.5" src="/images/sy_icon1.png">
                            <a class="mt-[5px]">企业直通车</a>
                        </div>
                        <div class="flex flex-col justify-center items-center qyzx-border cursor-pointer"
                        @click="$router.push({ name: ECOLUMN_CODE.COMPANY_SERVICE_RULE_KNOWLEDGE, })">
                            <img class="w-12.5 h-12.5" src="/images/sy_icon2.png">
                            <a class="mt-[5px]">助企政策</a>
                        </div>
                        <div class="flex flex-col justify-center items-center qyzx-border py-3 cursor-pointer"
                        @click="toDataFill">
                            <img class="w-12.5 h-12.5" src="/images/sy_icon3.png">
                            <a class="mt-[5px]">数据填报</a>
                        </div>
                        <div class="flex flex-col justify-center items-center qyzx-border"
                        @click="$router.push({ name: ECOLUMN_CODE.COMPANY_REPORT_MEETING, })">
                            <img class="w-12.5 h-12.5" src="/images/sy_icon4.png">
                            <a class="mt-[5px]">会务系统</a>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="flex items-center justify-between pb-2 border_b">
                        <span class="text-[#397fb7] text-5 font-bold cursor-pointer ">核电科普</span>
                    </div>
                    <div class="pt-3">
                        <img src="/images/sy_link1.png" class="cursor-pointer"
                            @click="$router.push({ path: '/science/museum' })">
                        <img src="/images/sy_link2.png" class="my-[12px] cursor-pointer"
                            @click="$router.push({ path: '/science/digital' })">
                        <img src="/images/sy_link3.png" class="cursor-pointer"
                            @click="$router.push({ path: '/science/charm' })">
                    </div>
                </div>
            </div>
        </div>

        <div class="absolute bottom-[20px] right-[-55px]">
            <div class="flex flex-col gap-4">
                <!-- <el-popover placement="left" width="110px" :popper-style="{ minWidth: '100px' }"
                    popper-class="flex justify-center items-center">
                    <template #reference>
                        <div class="wx-code w-[50px] h-[50px] leading-[50px] !rounded-full">
                            <i class="iconfont icon-shouji text-white text-[28px]" />
                        </div>
                    </template>
                    <template #default>
                        <img src="/images/code.png">
                    </template>
                </el-popover> -->
                <el-popover placement="left" width="110px" :popper-style="{ minWidth: '100px' }"
                    popper-class="flex justify-center items-center">
                    <template #reference>
                        <div class="wx-code w-[50px] h-[50px] leading-[50px] !rounded-full">
                            <i class="iconfont icon-weixin3 text-white text-[28px]" />
                        </div>
                    </template>
                    <template #default>
                        <img src="/images/code.png">
                    </template>
                </el-popover>
            </div>
        </div>


    </div>
</template>

<script setup lang="ts">
import { ECOLUMN_CODE, useColumnStore } from '~/store/column';

const config = useRuntimeConfig()
const about = ref<IRelease>()
useReleaseList(ECOLUMN_CODE.ORG_ABOUT, 1, 1).then(res => {
    about.value = res.at(0)
})

const toDataFill = () => {
    window.open('https://www.smnpo.cn:18081/datarpt/#/')
}
</script>

<style scoped>
.border_b {
    border-bottom: 2px solid;
    border-image: linear-gradient(90deg, rgba(216, 216, 216, 1), rgba(255, 255, 255, 1)) 2 2;
}

.org-info {
    @apply text-sm leading-[26px] pl-[10px];
    width: 190px;
    height: 202px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 8;
    line-clamp: 8;
    text-overflow: ellipsis;
}

.qyzx-border {
    box-shadow: 0px 0px 3px 0px rgba(55, 71, 251, 0.25);
    border-radius: 5px 5px 5px 5px;
}

.wx-code {
    background: linear-gradient(180deg, #428BC5 0%, #469BDE 100%);
    box-shadow: 0px 2px 4px 0px rgba(65, 120, 163, 0.5);
    border-radius: 0px 0px 0px 0px;
}
</style>