<template>
  <div class="ml-[12px] overflow-hidden pr-[12px]">
    <div class="relative py-[12px]">
      <div class="overflow-hidden rounded-[8px] bg-white px-[20px] pb-[40px] text-left">
        <h2 class="mt-[10px] overflow-hidden border-b border-[#F4F4F4] pb-[10px]">
          <span class="b_l text-[20px]">企业直通车</span>
        </h2>
        <h3 class="mb-[15px] ml-[10px] mt-[20px]">
          <span class="iconfont icon-suqiu1 gradient-text" />诉求信息
        </h3>
        <el-descriptions class="margin-top" :column="2" :label-width="150" border>
          <el-descriptions-item label-align="right" align="left">
            <template #label>
              单位名称
            </template>
            {{ form.companyName }}
          </el-descriptions-item>
          <el-descriptions-item label-align="right" align="left">
            <template #label>
              企业区划(注册地)
            </template>
            {{ getLabelFromDicts(company_train_registration_type, form.companyRegistrationPlace) }}
          </el-descriptions-item>
          <el-descriptions-item label-align="right" align="left">
            <template #label>
              联系人
            </template>
            {{ form.contact }}
          </el-descriptions-item>
          <el-descriptions-item label-align="right" align="left">
            <template #label>
              手机号
            </template>
            {{ form.contactWay }}
          </el-descriptions-item>
          <el-descriptions-item label-align="right" align="left">
            <template #label>
              邮箱
            </template>
            {{ form.email }}
          </el-descriptions-item>

          <el-descriptions-item label-align="right" align="left">
            <template #label>
              诉求类型
            </template>
            {{ getLabelFromDicts(company_train_demand_type, form.demandType) }}
          </el-descriptions-item>
          <el-descriptions-item label-align="right" align="left">
            <template #label>
              诉求时间
            </template>
            {{ dateFormat(form.demandTime) }}
          </el-descriptions-item>
          <el-descriptions-item label-align="right" align="left">
            <template #label>
              诉求标题
            </template>
            {{ form.demandTitle }}
          </el-descriptions-item>
          <el-descriptions-item label-align="right" align="left" :span="2">
            <template #label>
              诉求具体内容
            </template>
            {{ form.demandContent }}
          </el-descriptions-item>
          <el-descriptions-item label-align="right" align="left" :span="2">
            <template #label>
              附件
            </template>
            <the-uploader ref="publicityUploadRef" :obj-id="form.id" :show-upload="false"
              :show-delete="false"></the-uploader>
          </el-descriptions-item>
        </el-descriptions>
        <h3 class="mb-[15px] ml-[10px] mt-[20px]">
          <span class="iconfont icon-jinduguanli gradient-text" />处理进度
        </h3>
        <div class="mt-[20px] flex pb-[60px] ml-[10px]">
          <div class="relative">
            <div class="ciclebg hover">
              <span class="iconfont icon-fangyishenbao" />
            </div>
            <div class="text_bottom">
              诉求申报<br /> <em class="text-[#286BBD]">{{ getPendingLog?.reason }}</em>
              <p class="text-[#888]">
                {{ dateFormat(getPendingLog?.auditTime) }}
              </p>
            </div>
          </div>
          <div class="line" :class="{ 'hover': getProcessedLog?.status == ECOMPANY_TRAIN_STATUS.PROCESSED }">
            <span class="iconfont icon-jiantou1" />
          </div>
          <div class="relative">
            <div class="ciclebg " :class="{
              'hover': getProcessedLog?.status == ECOMPANY_TRAIN_STATUS.PROCESSED,
              'errorbg': form.status == ECOMPANY_TRAIN_STATUS.WITHDRAW
            }">
              <span class="iconfont icon-daichuli" />
            </div>
            <div class="text_bottom error">
              诉求受理
              <br />
              <em v-if="getProcessedLog?.status == ECOMPANY_TRAIN_STATUS.WITHDRAW">{{ getProcessedLog?.reason }}</em>
              <p class="text-[#888]">
                {{ dateFormat(getProcessedLog?.auditTime) }}
              </p>
            </div>
          </div>
          <div class="line" :class="{ 'hover': getCompletedLog?.status == ECOMPANY_TRAIN_STATUS.COMPLETED }">
            <span class="iconfont icon-jiantou1" />
          </div>
          <div class="relative">
            <div class="ciclebg" :class="{ 'hover': getCompletedLog?.status == ECOMPANY_TRAIN_STATUS.COMPLETED }">
              <span class="iconfont icon-gongdan_banjie" />
            </div>
            <div class="text_bottom">
              诉求办结<br /> <em
                v-if="getCompletedLog?.status == ECOMPANY_TRAIN_STATUS.COMPLETED">{{ getCompletedLog?.reason ||
                '已办结'}}</em>

              <p class="text-[#888]">
                {{ dateFormat(getCompletedLog?.auditTime) }}
              </p>
              <div class="text-sm text-[#666]">
                {{ getCompletedLog?.reason }}
              </div>
            </div>
          </div>
        </div>
        <div class="mt-[40px] overflow-hidden text-center">
          <el-button type="info" plain @click="$router.back()">
            返回
          </el-button>
          <el-button v-if="getCompletedLog?.status == ECOMPANY_TRAIN_STATUS.COMPLETED" type="primary" plain
            @click="$router.push({ name: ECOLUMN_CODE.COMPANY_SERVICE_TRAIN_EVALUATION, query: { id: form.id } })">
            {{ isEval ? '查看评价' : '去评价' }} 
          </el-button>
          <el-button type="primary" @click="$router.replace({ name: ECOLUMN_CODE.COMPANY_SERVICE_TRAIN_EDIT })">
            再次申报
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import { getDetail } from '~/api/company/serviceEvaluation';
import { getCompanyTrain, getLogListByTrainId } from '~/api/company/train';
import { ECOLUMN_CODE } from '~/store/column';


// 企业直通车查看
definePageMeta({
  name: ECOLUMN_CODE.COMPANY_SERVICE_TRAIN_VIEW,
  layout: 'company'
})

const { company_train_registration_type, company_train_demand_type } = useDict('company_train_registration_type', 'company_train_demand_type')

const id = useRoute().query.id as string
const loading = ref(true)
const form = reactive({} as ICompanyTrain)
const logList = ref<ICompanyTrainAuditLog[]>([])
const isEval = ref(false)
if (id) {
  const res = await getCompanyTrain(id)
  Object.assign(form, res.data)

  const evaluationRes = await getDetail(id)
  if (evaluationRes.data) {
    isEval.value = true
  }

  getLogListByTrainId(id).then((res) => {
    logList.value = res.data ?? []
  })
}

const getPendingLog = computed(() => {
  for (let i = logList.value.length - 1; i >= 0; i--) {
    if (logList.value[i].status == ECOMPANY_TRAIN_STATUS.PENDING) {
      return logList.value[i]
    }
  }
})

const getProcessedLog = computed(() => {
  for (let i = logList.value.length - 1; i >= 0; i--) {
    if (logList.value[i].status == ECOMPANY_TRAIN_STATUS.PENDING) {
      return null
    }
    if (logList.value[i].status == ECOMPANY_TRAIN_STATUS.PROCESSED) {
      return logList.value[i]
    }
    if (logList.value[i].status == ECOMPANY_TRAIN_STATUS.WITHDRAW) {
      return logList.value[i]
    }
  }
})

const getCompletedLog = computed(() => {
  for (let i = logList.value.length - 1; i >= 0; i--) {
    if (logList.value[i].status == ECOMPANY_TRAIN_STATUS.PENDING) {
      return null
    }
    if (logList.value[i].status == ECOMPANY_TRAIN_STATUS.PROCESSED) {
      return null
    }
    if (logList.value[i].status == ECOMPANY_TRAIN_STATUS.COMPLETED) {
      return logList.value[i]
    }
  }
})
</script>

<style scoped>
:deep(.custom-descriptions) .el-descriptions-item__content {
  white-space: pre-wrap;
}

:deep(.custom-descriptions) .el-descriptions-item__cell {
  width: 100%;
  /* 强制占满容器宽度 */
}

.ciclebg {
  @apply h-[60px] w-[60px] rounded-full bg-[#FAFAFA] text-center shadow-lg;
}

.ciclebg span {
  @apply text-[30px] text-[#CCCCCC] leading-[60px] cursor-pointer;
  transition: all 0.3s ease-in-out;
}

.ciclebg:hover {
  background: linear-gradient(180deg, #36b0ff 0.01%, #0080d4 111.97%);
}

.ciclebg:hover span {
  @apply text-white;
}

.ciclebg.hover {
  background: linear-gradient(180deg, #36b0ff 0.01%, #0080d4 111.97%);
}

.ciclebg.hover span {
  @apply text-white;
}

.text_bottom {
  @apply absolute left-[2px] mt-[15px] w-[200px] text-sm;
}

.text_bottom em {
  @apply text-[#286BBD] not-italic;
}

.text_bottom.error em {
  @apply text-[#fa7373];
}

.errorbg {
  background: linear-gradient(180deg, #fa7373 0.01%, #ec3a3a 111.97%);
}

.errorbg span {
  @apply text-white;
}

.errorbg:hover {
  background: linear-gradient(180deg, #fa7373 0.01%, #ec3a3a 111.97%);
}

.line {
  @apply mt-[30px] h-[10px] w-[350px] border-t border-[#E2E2E2] border-dashed text-center;
}

.line span {
  @apply absolute mt-[-12px] inline-block;
  color: #666;
}

.line.hover {
  @apply border-[#36b0ff];
}

.line.hover span {
  color: #36b0ff;
}

.gradient-text {
  background-image: linear-gradient(to right, #36b0ff, #0080d4);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.gradient-text-grey {
  background-image: linear-gradient(to right, #36b0ff, #0080d4);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.el-button.is-plain {
  background-color: #e6f3ff;
}

.el-button--success.is-text {
  color: #0080d4;
}

.el-button--primary {
  background: linear-gradient(90deg, #36b0ff 0%, #0080d4 100%);
  padding: 0 20px;
}

.el-button.is-plain {
  background: #e6f3ff;
  padding: 0 30px;
}

.el-button {
  border: none;
}

.el-button--info {
  background: #f4f4f4 !important;
  padding: 0 40px !important;
}

.el-button:hover {
  color: #0080d4;
}
</style>