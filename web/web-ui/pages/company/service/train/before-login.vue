<script lang="ts" setup>
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { ref } from 'vue'
import { ECOLUMN_CODE } from '~/store/column'

// 企业直通车列表
definePageMeta({
  name: ECOLUMN_CODE.COMPANY_SERVICE_TRAIN_BEFORE_LOGIN,
  layout: 'company',
})

// 注册 ArrowRight 图标
const { ArrowRight } = ElementPlusIconsVue

const activeIndex = ref('1')

function handleSelect(index: string) {
  // 处理菜单选择逻辑
  console.log(index)
}
</script>

<template>
  <Header />
  <Menu />
  <div class="neiye_enterprise bg_enterprise">
    <div class="mt-[8px] flex">
      <Enterprise />
      <div class="ml-[12px] flex-1 overflow-hidden pr-[12px]">
        <div class="relative py-[12px]">
          <div class="overflow-hidden rounded-[8px] bg-white px-[10px] pb-[10px] text-left">
            <div class="beforebg mt-[10px] p-[20px]">
              <div class="h-full rounded-[10px] bg-white/70 px-[50px] pb-[30px]">
                <h1 class="ys_font tit border-b border-[#9EC3E1] py-[15px] text-center text-[48px] pt-[40px]">
                  企业直通车登录说明
                </h1>
                <div class="mx-[30px]">
                  <p class="mt-[20px] indent-9 text-[18px] leading-[36px] text-justify">
                 本市核电企业可通过“上海一网通办”，选择“法人一证通”登录或使用企业账号登录系统。非上海核电企业可联系市核电办指定工作人员申请开通账号后，通过“非上海企业”入口完成身份验证并登录。企业登录成功后，即可在线提交诉求、上传相关材料，并随时查询办理进度。系统正式上线后，平台还将新增微信账号绑定功能，届时企业用户可通过微信便捷登录后使用相关服务。
                  </p>
                
                  <div class="mt-[20px] border-t border-[#ccc] pt-[20px] text-[#0091EF]">
                    <span class="iconfont icon-yonghu" /> 联系人:梁老师 <span class="iconfont icon-jisuanqi ml-[20px]" />
                    联系方式:021-64173231
                  </div>
                </div>
                <div class="mx-auto text-center mt-[25px]">
                  <el-button type="primary" class="mt-[30px] rounded-full"
                    @click="$router.push({ path: '/company/login' })">
                    <span class="mr-[5px] h-[20px] w-[20px] rounded-full bg-white leading-[20px]"><em
                        class="iconfont icon-qiye text-[#FFAA3A]" /></span> 点击进入 &nbsp; <span
                      class="iconfont icon-longjiantou text-[16px]" />
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <Bottom />
</template>

<style scoped>
.beforebg {
  background: url(/public/images/beforelgbg.png) no-repeat center center;
  background-size: cover;
  height: 600px;
}

.el-button--primary {
  background: linear-gradient(90deg, #ffb157 0%, #ff8800 100%);

  width: 456.92px;
  border: none;
  transition: all 0.3s;
}

.el-button--primary:hover {
  background: linear-gradient(90deg, #ff870e 0%, #ffb157 100%);
}

.el-menu--horizontal {
  --el-menu-horizontal-height: 60px;
}

.el-menu--horizontal>.el-menu-item {
  font-size: 16px;
}

.tit {
  background: linear-gradient(180deg, #36b0ff 0.01%, #0080d4 111.97%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;

  /* text-shadow: 0px 4px 4px #d1e4e8; */
}
</style>
