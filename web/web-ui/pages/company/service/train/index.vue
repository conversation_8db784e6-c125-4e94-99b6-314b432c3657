<template>

  <div class="ml-[12px] overflow-hidden pr-[12px] mb-[100px]">
    <div class="relative py-[12px]">
      <div class="overflow-hidden rounded-[8px] bg-white px-[10px] text-left">
        <h2 class="mt-[15px] overflow-hidden border-b border-[#F4F4F4] pb-[10px] mx-[10px]">
          <span class="b_l text-[18px]">企业直通车</span>
        </h2>
        <el-form :inline="true" :model="searchForm" class="demo-form-inline mt-5">
          <el-form-item label="诉求标题">
            <el-input v-model="searchForm.demandTitle" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="诉求类型">
            <el-select v-model="searchForm.demandType" placeholder="请选择" clearable>
              <!-- <el-option label="全部" value="" /> -->
              <el-option v-for="item in company_train_demand_type" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="诉求时间">
            <el-date-picker v-model="dateRange" value-format="YYYY-MM-DD" type="daterange" range-separator="至"
              start-placeholder="开始时间" end-placeholder="结束时间" />
          </el-form-item>
          <el-form-item label="处理结果">
            <el-select v-model="searchForm.status" placeholder="请选择" clearable>
              <el-option label="全部" value="" />
              <el-option v-for="item in getCompanyTrainStatus" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="handleSearch">
              搜索
            </el-button>
            <el-button type="info" :icon="Refresh" @click="handleClear">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="absolute right-[10px] top-[250px] z-10">
        <el-button type="primary" :icon="Plus" class="cursor-pointer"
          @click="$router.push({ name: ECOLUMN_CODE.COMPANY_SERVICE_TRAIN_EDIT })">
          新增诉求
        </el-button>
      </div>
      <el-tabs v-model="activeName" type="card" class="demo-tabs mt-[16px]" @tab-click="handleTabClick">
        <el-tab-pane label="已提交" name="first">
          <div class="overflow-hidden bg-white px-[16px] pb-[20px] pt-[10px]">
            <el-table :data="list" style="width: 100%">
              <el-table-column type="index" label="序号" width="55" />
              <el-table-column prop="demandTitle" label="诉求标题" show-overflow-tooltip align="center" />
              <el-table-column prop="demandType" label="诉求类型" width="120" align="center"
                :formatter="(row) => getLabelFromDicts(company_train_demand_type, row.demandType)" />
              <el-table-column prop="demandTime" label="诉求时间" width="240" align="center" :formatter="row => dateFormat(row.demandTime)" />
              <el-table-column prop="status" label="处理结果" width="150" align="center"
                :formatter="row => getLabelFromDicts(company_train_status, row.status)" />
              <el-table-column label="操作" width="200" align="center">
                <template #default="scope">
                  <el-button link size="small" @click="$router.push({ name: ECOLUMN_CODE.COMPANY_SERVICE_TRAIN_VIEW, query: { id: scope.row.id } })">
                    查看
                  </el-button>
                  <el-button v-if="scope.row.status === ECOMPANY_TRAIN_STATUS.PENDING" class="!text-[#F56C6C]" link size="small" @click="handleRecall(scope.row.id)">
                    撤回
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="mt-[15px] flex justify-center">
              <the-pagination :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
                @pagination="search" layout="prev, pager, next"></the-pagination>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="已办结" name="second">
          <div class="overflow-hidden bg-white px-[16px] pb-[20px] pt-[10px]">
            <el-table :data="list1" style="width: 100%">
              <el-table-column type="index" label="序号" width="55" />
              <el-table-column prop="demandTitle" label="诉求标题" show-overflow-tooltip align="center" />
              <el-table-column prop="demandType" label="诉求类型" width="120" align="center"
                :formatter="(row) => getLabelFromDicts(company_train_demand_type, row.demandType)" />
              <el-table-column prop="demandTime" label="诉求时间" width="240" align="center" :formatter="row => dateFormat(row.demandTime)" />
              <el-table-column prop="status" label="处理结果" width="150" align="center"
                :formatter="row => getLabelFromDicts(company_train_status, row.status)" />
              <el-table-column label="操作" width="200" align="center">
                <template #default="scope">
                  <el-button link size="small" @click="$router.push({ name: ECOLUMN_CODE.COMPANY_SERVICE_TRAIN_VIEW, query: { id: scope.row.id } })">
                    查看
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="mt-[15px] flex justify-center">
              <the-pagination :total="total1" v-model:page="page1.pageNum" v-model:limit="page1.pageSize"
                @pagination="search1" layout="prev, pager, next"></the-pagination>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="草稿" name="third">
          <div class="overflow-hidden bg-white px-[16px] pb-[20px] pt-[10px]">
            <el-table :data="list2" style="width: 100%">
              <el-table-column type="index" label="序号" width="55" />
              <el-table-column prop="demandTitle" label="诉求标题" show-overflow-tooltip align="center" />
              <el-table-column prop="demandType" label="诉求类型" width="120" align="center"
                :formatter="(row) => getLabelFromDicts(company_train_demand_type, row.demandType)" />
              <el-table-column prop="demandTime" label="诉求时间" width="240" align="center" :formatter="row => dateFormat(row.demandTime)" />
              <el-table-column prop="status" label="处理结果" width="150" align="center"
                :formatter="row => getLabelFromDicts(company_train_status, row.status)" />
              <el-table-column label="操作" width="200" align="center">
                <template #default="scope">
                  <el-button link size="small" @click="$router.push({ name: ECOLUMN_CODE.COMPANY_SERVICE_TRAIN_EDIT, query: { id: scope.row.id } })">
                    编辑
                  </el-button>
                  <el-button link size="small"  class="!text-[#F56C6C]" @click="handleRemove(scope.row.id)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="mt-[15px] flex justify-center">
              <the-pagination :total="total2" v-model:page="page2.pageNum" v-model:limit="page2.pageSize"
                @pagination="search2" layout="prev, pager, next"></the-pagination>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
  <!-- 页面内容 -->
    <el-dialog v-model="dialogVisible" width="30%" :close-on-click-modal="false" :align-center="false" top="23vh">
      <div class="px-[20px] h-[140px] mt-[5px]">
         <h1 class="text-[16px] font-bold">欢迎登录上海市核电办公室门户网站企业中心！</h1>
         <p class="leading-6 mt-[10px] text-left indent-7">为进一步完善单位信息，提升后续登录和使用便捷性，请您首次登录后及时<span class="text-[#088ce7]">设置登录密码</span>，并绑定相关负责人员手机号或微信，后续可通过手机号或微信快捷登录系统。</p>
<p class="leading-6  text-left  indent-7">同时，为了解企业情况，请及时填报产业名录信息。</p>
<p class="leading-6 text-left  indent-7">填报路径：<span class="text-[#088ce7]">【资料上报】</span>><span class="text-[#088ce7]">【产业名录信息维护】</span>。</p>

      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleBeforeClose">关闭</el-button>
        </span>
      </template>
    </el-dialog>
</template>

<script setup lang="ts" name="company-service-train">
import {
  Plus,
  Refresh,
  Search,
} from '@element-plus/icons-vue'
import { reactive, ref } from 'vue'
import { ECOLUMN_CODE } from '~/store/column'
import { getPage, recall, remove,getChinaDict } from '~/api/company/train'
import { isDotDotDotToken } from 'typescript'
import { has } from 'lodash'
// import axios from 'axios'
// 企业直通车列表
definePageMeta({
  name: ECOLUMN_CODE.COMPANY_SERVICE_TRAIN,
  layout: 'company',
})
const { company_train_demand_type, company_train_status } = useDict('company_train_demand_type', 'company_train_status')
const dialogVisible = ref(false)

const activeName = ref('first')
const getCompanyTrainStatus = computed(() => {
  if (activeName.value == 'first') {
    return company_train_status.value?.filter((item: any) => item.value == '1' || item.value == '2' || item.value == '4')
  } else if (activeName.value == 'second') {
    return company_train_status.value?.filter((item: any) => item.value == '3' || item.value == '5' || item.value == '10')
  } else if (activeName.value == 'third') {
    return company_train_status.value?.filter((item: any) => item.value == '0')
  }
})

const dateRange = ref<string[]>([])
const searchForm = reactive({

} as ICompanyTrainSearchVo)

const fun = (status: string[]) => {

  const total = ref(0)
  const page = reactive({
    pageNum: 1,
    pageSize: 10,
  })
  const list = ref<ICompanyTrainDto[]>([])
  const search = () => {
    const data = Object.assign({}, searchForm)
    data.status = (data.status?.length??0) > 0 ? data.status : status
    data.demandStartTime = dateRange.value?.at(0) ?? ''
    data.demandEndTime = dateRange.value?.at(1) ?? ''
    getPage(data, page).then((res) => {
      total.value = res.total ?? 0
      list.value = res.rows ?? []
    })
  }

  return {
    total,
    page,
    list,
    search,
  }
}

const { total, page, list, search } = fun(['1', '2', '4'])
search()

const { total: total1, page: page1, list: list1, search: search1 } = fun(['3', '5', '10'])
search1()

const { total: total2, page: page2, list: list2, search: search2 } = fun(['0', ])
search2()


const handleTabClick = () => {
  searchForm.status = []
  handleSearch()
}
   
const handleSearch = () => {
  if (activeName.value === 'first') {
    search()
  } else if (activeName.value === 'second') {
    search1()
  } else if (activeName.value === 'third') {
    search2()
  }
}
const handleClear = () => {
  searchForm.demandTitle = ''
  searchForm.demandType = ''
  searchForm.status = []
  dateRange.value = []
  search()
}

const handleRemove = (id: string) => {
  ElMessageBox.confirm('确定删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    remove(id).then(() => {
      ElMessage.success('删除成功')
      handleSearch()
    })
  })
}

const handleRecall = (id: string) => {
  ElMessageBox.confirm('确定撤回吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    recall({trainId: id}).then(() => {
      ElMessage.success('撤回成功')
      handleSearch()
    })
  })
}
// onMounted(() => {
//   // 在组件挂载后，将dialogVisible设置为true，对话框就会弹出
//   dialogVisible.value = true
// })
const neverShowAgain = ref(false)
const firstLogin = ref(true) // 是否首次登录

// 页面加载时检查登录状态
onMounted(() => {
  // 检查是否已经关闭过弹窗
  const hasClosed = localStorage.getItem('welcomeDialogClosed')
    console.log( "测试",hasClosed)
    console.log( "测试",!hasClosed)
  // 如果没有关闭过，才检查接口
  // checkFirstLogin()
  if (!hasClosed || hasClosed === 'null') {
    console.log( "测试1")
    checkFirstLogin()
  }
})

window.addEventListener('storage', (e) => {
  if (e.key === 'welcomeDialogClosed') {
    dialogVisible.value = localStorage.getItem('welcomeDialogClosed') !== 'true';
  }
});

// 首次登录检查
const checkFirstLogin = async () => {
  console.log( "测试2")
  try {
    // 调用接口检查是否首次登录
    const response = await getChinaDict()
    console.log(response, "data")
    // 接口返回 true 表示需要显示弹窗
    if (response?.data === true) {
      // 延迟显示弹窗，提高用户体验
      localStorage.setItem('welcomeDialogClosed', 'true')
      setTimeout(() => {
        dialogVisible.value = true
        firstLogin.value = true
      }, 500)
    }
  } catch (error) {
    console.error('获取首次登录状态失败:', error)
  }
}

// 关闭弹窗
const closeDialog = () => {
  dialogVisible.value = false
  localStorage.setItem('welcomeDialogClosed', 'true')
  
  // 保存用户设置
  // if (neverShowAgain.value) {
    
  // } else {
  //   // 如果不选不再显示，仅记录关闭过，下次登录检查接口
  //   localStorage.setItem('welcomeDialogClosed', 'false')
  // }
}

// 处理弹窗关闭前的操作
const handleBeforeClose = () => {
  // 保存设置
  closeDialog()
}
</script>
<style scoped>
:deep(.demo-form-inline .el-input) {
  --el-input-width: 220px;
}

:deep(.demo-form-inline .el-select) {
  --el-select-width: 220px;
}

:deep(.el-button--primary) {
  background: linear-gradient(90deg, #36b0ff 0%, #0080d4 100%);
  border: none;
}

:deep(.el-button--info) {
  background-color: #f4f4f4;
  color: #676767;
  border: none;
}

:deep(.el-form-item__label) {
  width: 80px;
}

:deep(.el-sub-menu__title) {
  color: #666;
}

:deep(.el-menu-item) {
  color: #666;
}

:deep(.el-tabs__item) {
  background-color: #d2e8f7;
  margin: 0 3px;
  border: none;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
  border: none;
}

:deep(.el-tabs__item.is-active) {
  background-color: #318ad9;
  color: #fff !important;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
  border-radius: 10px 10px 0 0;
  border-bottom: none;
  color: #4d4d4d;
  text-align: center;
  padding: 0 30px;
}

:deep(.el-tabs--top.el-tabs--card > .el-tabs__header .el-tabs__item:last-child) {
  padding-right: 30px;
}

:deep(.el-tabs--top.el-tabs--card > .el-tabs__header .el-tabs__item:nth-child(2)) {
  padding-left: 30px;
}

:deep(.demo-tabs > .el-tabs__content) {
  color: #6b778c;

  margin-top: -15px;
}

:deep(.el-tag) {
  color: #286bbd;
  font-weight: normal;
}

:deep(.el-table th.el-table__cell) {
  background-color: #e9f3fd;
  color: #5f5f5f;
}

:deep(.el-button.is-link) {
  color: #318ad9;
}

:deep(.el-button.is-link:hover) {
  color: #057ae2;
}
</style>