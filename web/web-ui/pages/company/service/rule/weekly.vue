<template>
  <div class="mt-2.5 mb-150px">
    <el-form ref="formRef" :inline="true" :model="searchForm" class="demo-form-inline ml-10 mt-5"  label-width="60px">
      <el-form-item label="关键词">
        <el-input v-model="searchForm.deptName" class="w-60" placeholder="请输入关键词" />
      </el-form-item>
      <el-form-item label="时间" style="width: 310px;">
        <el-date-picker v-model="dateRange" type="daterange" value-format="YYYY-MM-DD" range-separator="至" start-placeholder="开始时间"
          end-placeholder="结束时间" />
      </el-form-item>
      
      <el-form-item label="政策来源" label-width="80px">
       <el-cascader v-model="searchForm.rank" :options="options" class="w-42" @change="handleChange" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" class="custom-height-btn" @click="search" >
          查询
        </el-button>
        <el-button type="info" plain class="custom-height-btn" @click="handleClear" >
          重置
        </el-button>
      </el-form-item>
    </el-form>
    <div class="ml-10 flex">
      <div class="mt-1.25 text-gray-600 w-[80px]">
        政策类型：
      </div>
      <div  class="align-left">
        <el-radio-group v-model="searchForm.type" size="large">
          <el-radio-button label="全部" value="" />
          <el-radio-button v-for="item in policy_type" :label="item.label" :value="item.value" />
        </el-radio-group>
      </div>
    </div>
    <div class="grid grid-cols-2 mb-5 ml-10 mr-10 mt-5 gap-6">
      <div v-for="item in list" @click="toPage(item)"
        class="flex cursor-pointer justify-between rounded-lg bg-gray-50 border from-[#F4F9FE] to-[#fff] p-5 transition hover:border hover:border-[#0080D4] hover:shadow-[0_15px_30px_-15px_rgba(28,127,187,0.3)]">
        <div class="flex-1">
          <h2 class="text-gray-700 font-bold">
            {{ item.deptName }}<el-tag type="primary" size="small" class="ml-[10px] font-normal">
              {{ getLabelFromDicts(policy_type, item.type) }}
            </el-tag>
          </h2>
          <p class="line-clamp-2 mt-2.5 text-sm text-gray-600">
            {{ item.fileName }}
          </p>
          <p class="mt-2.5 text-sm text-gray-400">
            {{ item.fileNumber }}
          </p>
        </div>
        <div class="ml-5 mt-7.5">
          <img :src="item.qrcodeUrl" width="80px" class="border border-2 border-white">
        </div>
      </div>
      
    </div>
    <div class="flex justify-center mb-2">
      <the-pagination :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize" @pagination="search"
            layout="prev, pager, next"></the-pagination>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  Refresh,
  Search,
} from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import { getPage,getDictList } from '~/api/company/policy'
import QRCode from 'qrcode'
const route = useRoute();
const router = useRouter();
// 级联选择器绑定的值
const value = ref([])

// 级联选择器的选项（动态数据）
const options = ref([])

// 处理选择变化
const handleChange = (value:any) => {
  console.log(value)
}



// 每周政策汇编
definePageMeta({
  name: 'COMPANY_SERVICE_RULE_WEEKLY',
  layout: 'company'
})
// 获取字典数据并转换为级联选择器需要的结构
const fetchDictData = async () => {
  try {
    const res = await getDictList('policy_detail_rank')
    const dictData = res.data || []
    
    // 转换数据结构
    options.value = transformDictToCascader(dictData)
  } catch (error) {
    console.error('获取字典数据失败:', error)
    options.value = [] // 失败时设置为空数组
  }
}

// 字典数据转换为级联选择器结构
const transformDictToCascader = (dictData:any) => {
  const result:any= []
  const childrenMap = new Map()
  
  // 第一遍遍历：收集所有父节点
  dictData.forEach((item:any) => {
    if (!item.remark || item.remark == "区级" || item.remark == "市级" || item.remark == "国家级") {
      result.push({
        value: item.dictValue,
        label: item.dictLabel,
        children: []
      })
      // 建立映射关系
      childrenMap.set(item.dictId, result[result.length - 1].children)
    }
  })
  
  // 第二遍遍历：处理子节点
  dictData.forEach((item:any) => {
    if (item.remark && item.remark !== "区级" && item.remark !== "市级" && item.remark !== "国家级") {
      const parent = childrenMap.get(item.parentId)
      if (parent) {
        parent.push({
          value: item.dictLabel,
          label: item.dictLabel
        })
      }
    }
  })
  
  return result
}
const { policy_type } = useDict('policy_type')

const dateRange = ref<string[]>([])
const total = ref<number>(0)
const list = ref<(ICompanyPolicyDetail&{qrcodeUrl?: string})[]>([])
const page = reactive({
  pageNum: 1,
  pageSize: 10
})
const searchForm = reactive<ICompanyPolicyDetailVo>({
  deptName: '',
  type: ''
} as ICompanyPolicyDetailVo)


const search = () => {
  searchForm.publishStartDate = dateRange.value?.at(0) ?? ''
  searchForm.publishEndDate = dateRange.value?.at(1) ?? ''
  getPage({...searchForm, rank: searchForm.rank?.at(-1)??''}, page).then((res) => {
    total.value = res.total ?? 0
    list.value = []
    res.rows?.forEach(async t => {
      list.value.push({
        ...t,
        qrcodeUrl: await QRCode.toDataURL(t.url, {margin: 0})
      })
    
    })

  })
}
search()
watch(() => searchForm.type, (val) => {
  search()
})

const formRef = ref<FormInstance>()
const handleClear = () => {
  formRef.value?.resetFields()
  dateRange.value = []
  search()
}

const toPage = (row: ICompanyPolicyDetail) => {
  if (row.url && row.url.includes('http'))
    window.open(row.url)
}
onMounted(() => {
  fetchDictData()
})
</script>
<style scoped>
.el-button--primary {
  background: linear-gradient(90deg, #36b0ff 0%, #0080d4 100%);
  padding: 0 20px;
}

.el-button--primary:hover {
  background: linear-gradient(90deg, #149df6 0%, #0377c3 100%);
}

.el-menu--horizontal {
  --el-menu-horizontal-height: 60px;
}

.el-menu--horizontal>.el-menu-item {
  font-size: 16px;
}

:deep(.el-radio-button__inner) {
  border: none;
}

:deep(.el-radio-button:last-child .el-radio-button__inner) {
  border-radius: 0;
}

:deep(.el-radio-button:first-child .el-radio-button__inner) {
  border-radius: 0;
  border: none;
}

:deep(.el-radio-button.is-active .el-radio-button__original-radio:not(:disabled) + .el-radio-button__inner) {
  background-color: #318ad9;
  border-radius: 4px;
}

:deep(.el-radio-button--large .el-radio-button__inner) {
  font-weight: normal;
}

:deep(.el-radio-button__inner:hover) {
  color: #318ad9;
}

:deep(.el-radio-button--large .el-radio-button__inner) {
  padding: 8px 15px;
}
.el-form--inline .el-form-item{margin-right:30px;}
:deep(.el-form-item__label){font-size: 16px;}
</style>