<template>
    <div class="ml-[12px] overflow-hidden pr-[12px]">
      <div class="relative py-[12px]">
        <div class="overflow-hidden rounded-[8px] bg-white px-[10px] pb-[10px] text-left mb-[100px]">
          <el-menu default-active="1" mode="horizontal">
            <el-menu-item v-if="useAuth().hasPermissions(['company:user:shhdjb'])" index="1" @click="$router.push({ name: ECOLUMN_CODE.COMPANY_DATA_INDUSTRY_JOURNAL_BULLETIN })">
              《上海核电》简报
            </el-menu-item>
            <el-menu-item v-if="useAuth().hasPermissions(['company:user:ckxx'])" index="2" @click="$router.push({ name: ECOLUMN_CODE.COMPANY_DATA_INDUSTRY_JOURNAL_REFERENCE })">
              参考信息
            </el-menu-item>
          </el-menu>
          <NuxtPage></NuxtPage>
        </div>
      </div>
    </div>
  </template>
  <script setup lang="ts">
import { ECOLUMN_CODE } from '~/store/column';

  // 《上海核电》简报
  definePageMeta({
    name: 'COMPANY_DATA_INDUSTRY_JOURNAL',
    layout: 'company'
  })
  
  
  
  </script>
  <style scoped>
  .briefingbg {
    background: url(/images/lookup_bg1.png) no-repeat center top #edf6ff;
    background-size: cover;
    overflow: hidden;
    padding-top: 27px;
  }
  
  .briefingbg span {
    color: #1591ff;
    font-size: 24px;
    margin: 20px 60px;
    cursor: pointer;
  }
  
  .briefingbg span:hover {
    color: #0c70c8;
  }
  
  .briefingbg span.hover {
    background-color: #318ad9;
    color: #fff;
    padding: 8px 30px;
  }

  :deep(.el-sub-menu__hide-arrow) {
    display: none;
  }
  </style>
  