<template>
  <div class="knowledegebg">
    <div class="briefingbg arial_font mt-[20px] px-[20px] py-[10px] text-[#097bcb] overflow-hidden flex items-center">
      <div class="scroll-container w-full" ref="scrollContainer">
        <!-- 左箭头 -->
        <div class="scroll-arrow left" :class="{ hidden: !showLeftArrow }" @click="scrollLeft">
          &lt;
        </div>
        <!-- 年份容器 -->
        <div class="year" ref="yearContainer" :style="{ transform: `translateX(${-scrollPosition}px)` }">
          <span v-for="(year, index) in years" :key="year" :class="{ hover: activeYear === year }"
            @click="setActiveYear(year)">
            {{ year }}
          </span>
        </div>
        <!-- 右箭头 -->
        <div class="scroll-arrow right" :class="{ hidden: !showRightArrow }" @click="scrollRight">
          &gt;
        </div>
      </div>
    </div>
    <div class="px-[20px] py-[20px]">
      <div class="text-left">
        <div v-for="item in list"
          class="relative flex justify-between border-b border-[#eee] px-[5px] py-[12px] transition ease-in hover:bg-[#f3f9ff]"
          @click="handleView(item)">
          <span class="absolute top-[21px] h-[5px] w-[5px] bg-[#4792CD]" />
          <h1 class="ml-[15px] w-[70%]">
            <a class="line-clamp-1 cursor-pointer text-[16px]">{{ item.title }}</a>
          </h1>
          <div class="text-sm">
            <span class="ml-[4px] text-gray-400"><i class="iconfont icon-shijian" /> {{ dateFormat(item.articleDate)
              }}</span>
          </div>
        </div>
        <div class="mt-[20px] flex justify-center">
          <the-pagination :total="total" v-model:page="searchForm.pageNum" v-model:limit="searchForm.pageSize" :current-page="searchForm.pageNum"
            @pagination="search" layout="prev, pager, next"></the-pagination>
        </div>
      </div>
    </div>
    <!-- <el-dialog v-model="fileViewShow" width="800px">
      <template #title>
        <span class="text-[16px]">查看文件</span>
      </template>
      <iframe :src="fileUrl" class="w-full h-[600px]" />
    </el-dialog> -->
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import { ECOLUMN_CODE } from '~/store/column';
import { getContentReleaseYearsInterval } from '~/api/release';
// 参考信息
definePageMeta({
  name: ECOLUMN_CODE.COMPANY_DATA_INDUSTRY_JOURNAL_REFERENCE,
  layout: 'company'
})

const config = useRuntimeConfig()


// 响应式数据
const years = ref<number[]>([]); // 动态生成的年份数组
const activeYear = ref<number>(dayjs().year()); // 当前选中的年份
const scrollPosition = ref(0);
const showLeftArrow = ref(false);
const showRightArrow = ref(false);
const yearContainer = ref<HTMLDivElement | null>(null);
const scrollContainer = ref<HTMLElement | null>(null);
const containerWidth = ref(0);
const contentWidth = ref(0);
const total = ref(0);
const list = ref<IRelease[]>([]);
const searchForm = reactive({
  pageNum: 1,
  pageSize: 15,
});

// 设置当前激活年份
const setActiveYear = (year: number) => {
  searchForm.pageNum = 1;
  activeYear.value = year;
};


// 计算滚动状态
const updateScrollState = () => {
  if (scrollContainer.value && yearContainer.value) {
    containerWidth.value = scrollContainer.value.clientWidth;
    contentWidth.value = yearContainer.value.scrollWidth;
    showLeftArrow.value = scrollPosition.value > 0;
    showRightArrow.value = scrollPosition.value < contentWidth.value - containerWidth.value;
  }
};

// 向左滚动
const scrollLeft = () => {
  const itemWidth = 160; // 140px 宽度 + 20px 边距
  scrollPosition.value = Math.max(scrollPosition.value - itemWidth, 0);
  updateScrollState();
};

// 向右滚动
const scrollRight = () => {
  const itemWidth = 160; // 140px 宽度 + 20px 边距
  const maxScroll = contentWidth.value - containerWidth.value;
  scrollPosition.value = Math.min(scrollPosition.value + itemWidth, maxScroll);
  updateScrollState();
};

// 日期格式化函数
const dateFormat = (date: string | Date, format = 'YYYY-MM-DD') => {
  return dayjs(date).format(format);
};

// 获取年份范围数据
// const fetchYears = async () => {
//   try {
//     // 调用接口获取年份数量
//     const res = await getContentReleaseYearsInterval({
//       columnId: getColumnIdByCode(ECOLUMN_CODE.COMPANY_DATA_INDUSTRY_JOURNAL_REFERENCE),
//       sortKind: 'article_date',
//       sortAd: 'desc'
//     });
//     ;
//     // 根据接口返回的 data 值生成年份数组
//     const yearCount = parseInt(res.data == 'undefined' ? "0" : res.data) ?? 0;// 获取接口返回的年数
//     const currentYear = new Date().getFullYear(); // 获取当前年份

//     // 生成从当前年份开始递减的年份数组

//     years.value = Array.from({ length: yearCount }, (_, i) => currentYear - i);
//     // 确保当前选中年份在有效范围内
//     if (!years.value.includes(activeYear.value)) {
//       activeYear.value = years.value[0]; // 默认选中最新年份
//     }
//   } catch (err: any) {
//     console.error('获取年份范围失败:', err);
//   } finally {
//     updateScrollState(); // 更新滚动状态
//   }
// };

// const fetchYears = async () => {
//   const currentYear = new Date().getFullYear();
//   try {
//     // 调用接口获取年份数量
//     const res = await getContentReleaseYearsInterval({
//       columnId: getColumnIdByCode(ECOLUMN_CODE.COMPANY_DATA_INDUSTRY_JOURNAL_REFERENCE),
//       sortKind: 'article_date',
//       sortAd: 'desc'
//     });

//     const yearCount = parseInt(res.data) || 0;
//     const currentYear = new Date().getFullYear();
    
//     // 生成年份数组（从最早年到最新年）
//     const startYear = currentYear - yearCount + 1;
//     years.value = Array.from({ length: yearCount }, (_, i) => startYear + i)
//                   .sort((a, b) => a - b); // 升序排序

//     // 设置默认选中最早年
//     if (years.value.length > 0) {
//       activeYear.value = years.value[0]; // ✅ 关键：取第一项
//     } else {
//       activeYear.value = currentYear; // 兜底逻辑
//     }
//   } catch (err) {
//     console.error("获取年份失败", err);
//   }
//   return currentYear;
// };


const fetchYears = async () => {
  try {
    // 调用接口获取年份字符串
    const res = await getContentReleaseYearsInterval({
      columnId: getColumnIdByCode(ECOLUMN_CODE.COMPANY_DATA_INDUSTRY_JOURNAL_REFERENCE),
      sortKind: 'article_date',
      sortAd: 'desc'
    });

    // 1. 获取年份字符串（假设接口返回在res.msg字段）
    const yearString = res.msg || '';
    
    // 2. 分割字符串并转换为数字数组
    const yearArray = yearString
      .split(',')
      .map(item => {
        // 移除空格和非数字字符
        const cleanItem = item.trim().replace(/[^\d]/g, '');
        return parseInt(cleanItem);
      })
      .filter(year => !isNaN(year) && year > 2000 && year <= new Date().getFullYear());
    
    // 3. 升序排序并去重
    years.value = [...new Set(yearArray)].sort((a, b) => a - b);
    
    // 4. 设置默认选中最小年份
    if (years.value.length > 0) {
      activeYear.value = years.value[0]; // 取排序后的第一项（最小年份）
    } else {
      activeYear.value = new Date().getFullYear(); // 降级处理
      years.value = [activeYear.value]; // 确保年份数组不为空
    }
  } catch (err) {
    console.error("获取年份失败", err);
    // 降级处理：使用当前年
    activeYear.value = new Date().getFullYear();
    years.value = [activeYear.value];
  } finally {
    nextTick(updateScrollState); // 确保DOM更新后计算滚动状态
  }
}
const search = () => {
  useReleasePageByYear(ECOLUMN_CODE.COMPANY_DATA_INDUSTRY_JOURNAL_REFERENCE, activeYear.value, searchForm.pageNum, searchForm.pageSize).then(res => {
    total.value = res.total ?? 0
    list.value = res.rows ?? []
    //  fetchYears()
  })
}
onMounted(() => {
  fetchYears();
});
// 监听年份变化
watch(activeYear, () => {
  search();
}, { immediate: true });

const handleView = (row: IRelease) => {
  if (row.fileList && row.fileList.length > 0) {
    const filePath = config.public.backendApi + row.fileList.at(0)?.filePath;
    window.open(filePath, '_blank'); // 打开新窗口
  } else {
    ElMessage.warning('未查询到文件');
  }
}
// 初始化
onMounted(async () => {
    
    // 获取年份范围数据
    await fetchYears();

    // 初始化滚动状态
    updateScrollState();
    window.addEventListener('resize', updateScrollState);
});

onBeforeUnmount(() => {
    window.removeEventListener('resize', updateScrollState);
});
</script>
<style scoped>

.briefingbg {
  background: url(/images/lookup_bg1.png) no-repeat center top #edf6ff;
  background-size: cover;
  overflow: hidden;
  height: 80px;
}


.scroll-container {
  position: relative;
  overflow: hidden;
}

.year {
  display: flex;
  flex-wrap: nowrap;
  transition: transform 0.4s ease;
}

.year span {
  flex: 0 0 auto;
  margin: 0 10px;
  width: 140px;
  font-size: 26px;
  text-align: center;
  padding: 5px 0;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.year span:hover,
.year span.hover {
  background-color: #097bcb;
  color: #fff;
  cursor: pointer;
  margin: 0 10px;
  z-index: 2;
}

.scroll-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
  transition: all 0.3s ease;
}

.scroll-arrow:hover {
  background-color: #0f8ee9;
  color: white;
  transform: translateY(-50%) scale(1.1);
}

.scroll-arrow.left {
  left: 10px;
}

.scroll-arrow.right {
  right: 10px;
}

.scroll-arrow.hidden {
  opacity: 0;
  pointer-events: none;
}
</style>