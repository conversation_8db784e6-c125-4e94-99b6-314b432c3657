<template>
  <div class="px-[20px] py-[20px]">
    <div class="text-left">
      <div v-for="item in list"
        class="relative flex justify-between border-b border-[#eee] px-[5px] py-[12px] transition ease-in hover:bg-[#f3f9ff]"
        @click="handleView(item)">
        <span class="absolute top-[21px] h-[5px] w-[5px] bg-[#4792CD]" />
        <h1 class="ml-[15px] w-[70%]">
          <a class="line-clamp-1 cursor-pointer text-[16px]">{{ item.title }}</a>
        </h1>
        <div class="text-sm">
          <span class="ml-[4px] text-gray-400"><i class="iconfont icon-shijian" /> {{ dateFormat(item.articleDate)
            }}</span>
        </div>
      </div>
      <div class="mt-[20px] flex justify-center">
        <the-pagination :total="total" v-model:page="searchForm.pageNum" v-model:limit="searchForm.pageSize"
          @pagination="search" layout="prev, pager, next"></the-pagination>
      </div>
    </div>
    <!-- <el-dialog v-model="fileViewShow" width="800px">
      <template #title>
        <span class="text-[16px]">查看文件</span>
      </template>
      <iframe :src="fileUrl" class="w-full h-[600px]" />
    </el-dialog> -->
  </div>
</template>

<script setup lang="ts">
import { ECOLUMN_CODE } from '~/store/column'

// 季度运行分析报告
definePageMeta({
  name: ECOLUMN_CODE.COMPANY_DATA_REPORT_QUARTER,
  layout: 'company'
})

const config = useRuntimeConfig()
const total = ref(0)
const searchForm = reactive({
  pageNum: 1,
  pageSize: 10,
})
const list = ref<IRelease[]>([])
const search = () => {
  useReleasePage(ECOLUMN_CODE.COMPANY_DATA_REPORT_QUARTER, searchForm.pageNum, searchForm.pageSize).then(res => {
    total.value = res.total ?? 0
    list.value = res.rows ?? []
  })
}
search()

const handleView = (row: IRelease) => {
  if (row.fileList && row.fileList.length > 0) {
    const filePath = config.public.backendApi + row.fileList.at(0)?.filePath;
    window.open(filePath, '_blank'); // 打开新窗口
  } else {
    ElMessage.warning('未查询到文件');
  }
}
</script>