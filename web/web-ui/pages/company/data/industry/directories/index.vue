<template>
  <div class="ml-[12px]  overflow-hidden pr-[12px] mb-[100px]">
    <div class="relative py-[12px]">
      <div class="overflow-hidden rounded-[8px] bg-white px-[20px] text-left">
        <h2 class="mt-[15px] overflow-hidden border-b border-[#F4F4F4] pb-[10px]">
          <span class="b_l text-[18px]">产业名录</span>
        </h2>
        <el-form ref="formRef" :inline="true" :model="searchForm" class="demo-form-inline mt-[20px]">
          <el-form-item label="企业名称" prop="companyName">
            <el-input v-model="searchForm.companyName" placeholder="请输入企业名称" clearable />
          </el-form-item>
          <el-form-item label="类别" prop="type">
            <el-select v-model="searchForm.type" placeholder="请选择类别" clearable>
              <!-- <el-option label="全部" value="" /> -->
              <el-option v-for="item in industry_directories_type" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="search">
              搜索
            </el-button>
            <el-button type="info" :icon="Refresh" @click="handleClear">
              重置
            </el-button>
          </el-form-item>
        </el-form>
        <el-table :data="list" style="width: 100%">
          <el-table-column property="companyName" label="企业名称" />

          <el-table-column property="type" label="企业类别">
            <template #default="{ row }">
              {{ getLabelFromDicts(industry_directories_type, row.type) }}
            </template>
          </el-table-column>
          <el-table-column property="type" label="操作" width="100" align="center">
            <template #default="{ row }">
              <el-button color="#409EFF" plain text   @click="toDetail(row.id)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="my-[15px] flex justify-center">
          <the-pagination :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize" @pagination="search"
            layout="prev, pager, next"></the-pagination>
        </div>
      </div>
    </div>
    <!-- <el-dialog v-model="fileViewShow" width="800px">
      <template #title>
        <span class="text-[16px]">查看文件</span>
      </template>
      <iframe :src="fileUrl" class="w-full h-[600px]" />
    </el-dialog> -->
  </div>
</template>

<script setup lang="ts">
import {
  Plus,
  Refresh,
  Search,
} from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import { reactive, ref } from 'vue'
import { getPage } from '~/api/company/industryDirectories'
import { getFileListByObjId } from '~/api/file'
import { ECOLUMN_CODE } from '~/store/column'

// 产业名录
definePageMeta({
  name: 'COMPANY_DATA_INDUSTRY_DIRECTORIES',
  layout: 'company'
})

const config = useRuntimeConfig()
const { industry_directories_type } = useDict('industry_directories_type')

const total = ref<number>(0)
const list = ref<ICompanyIndustryDirectoriesDto[]>([])
const route = useRoute();
const router = useRouter();
const page = reactive({
  pageNum: 1,
  pageSize: 10
})
const searchForm = reactive({
  companyName: '',
  type: '',
  status: '0'
})
const search = () => {
  getPage(searchForm, page).then((res) => {
    total.value = res.total ?? 0
    list.value = res.rows ?? []

  })
}
search()

// const fileViewShow = ref(false)
// const fileUrl = ref('')
// const view = async (row: ICompanyIndustryDirectoriesDto) => {
//   const fileList = (await getFileListByObjId({ objId: row.id })).data
//   const file = fileList?.at(0)
//   if (file) {
//     fileViewShow.value = true
//     fileUrl.value = config.public.backendApi + file.filePath
//   } else {
//     ElMessage.warning('未查询到文件')
//   }
// }
// 跳转到详情页
const toDetail = async (id: any) => {
  // 保存当前页码到路由参数
  // router.replace({
  //   query: {
  //     ...route.query,
  //     // page: searchForm.pageNum.toString()
  //   }
  // })
  const fileList = (await getFileListByObjId({ objId: id })).data
  const file = fileList?.at(0)            
  
  if (file) {
    window.open(config.public.backendApi + file.filePath)
  } else {
    ElMessage.warning('未查询到文件')
  }
  // router.push({
  //   name: ECOLUMN_CODE.COMPANY_DATA_INDUSTRY_DETAIL,
  //   params: { id }
  // })
}



const formRef = ref<FormInstance>()

const handleClear = () => {
  formRef.value?.resetFields()
  search()
}
</script>
<style scoped>
:deep(.demo-form-inline .el-input) {
  --el-input-width: 220px;
}

:deep(.demo-form-inline .el-select) {
  --el-select-width: 220px;
}

:deep(.el-button--primary) {
  background: linear-gradient(90deg, #36b0ff 0%, #0080d4 100%);
  border: none;
}

:deep(.el-button--info) {
  background-color: #f4f4f4;
  color: #676767;
  border: none;
}

:deep(.el-form-item__label) {
  width: 80px;
}

:deep(.el-sub-menu__title) {
  color: #666;
}

:deep(.el-menu-item) {
  color: #666;
}

:deep(.el-tabs__item) {
  background-color: #d2e8f7;
  margin: 0 3px;
  border: none;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
  border: none;
}

:deep(.el-tabs__item.is-active) {
  background-color: #318ad9;
  color: #fff !important;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
  border-radius: 10px 10px 0 0;
  border-bottom: none;
  color: #4d4d4d;
  text-align: center;
  padding: 0 30px;
}

:deep(.el-tabs--top.el-tabs--card > .el-tabs__header .el-tabs__item:last-child) {
  padding-right: 30px;
}

:deep(.el-tabs--top.el-tabs--card > .el-tabs__header .el-tabs__item:nth-child(2)) {
  padding-left: 30px;
}

:deep(.demo-tabs > .el-tabs__content) {
  color: #6b778c;

  margin-top: -15px;
}

:deep(.el-tag) {
  color: #286bbd;
  font-weight: normal;
}

:deep(.el-table th.el-table__cell) {
  background-color: #e9f3fd;
  color: #5f5f5f;
}

:deep(.el-button.is-link) {
  color: #318ad9;
}

:deep(.el-button.is-link:hover) {
  color: #057ae2;
}
</style>