<!-- 详情页 (party-union-area-detail.vue) -->
<template>
  
       
  <div id="printSection" >
    <div class="px-[40px] text-[18px] pb-10 mt-[30px]">
    <!-- <TheZoom :show-back="true" @back="goBack"> -->
      <!-- <div :show-back="true"  @back="goBack" class="flex justify-start my-[10px]">
                <div class="bg-[#edf9ff] border-1 border-[#6dbadd] inline-block px-4 rounded cursor-pointer mt-2"
                    @click="$router.back()">
                    <span class="text-[#118ac1] text-3.5">返回</span>
                </div>
            </div> -->
      <iframe :src="fileUrl" class="w-full h-[600px]" />
      <!-- <ThePrintTool target-area-id="printSection" theme="red" /> -->
    <!-- </TheZoom> -->
  </div>
  </div>
</template>

<script lang="ts" setup>
import { getPage } from '~/api/company/industryDirectories'
import { useRoute, useRouter } from 'vue-router';
import { getFileListByObjId } from '~/api/file'
import { ECOLUMN_CODE } from '~/store/column';

definePageMeta({
  name: ECOLUMN_CODE.COMPANY_DATA_INDUSTRY_DETAIL,
  layout: 'company',
  submenuCode: ECOLUMN_CODE.COMPANY_DATA_INDUSTRY_DIRECTORIES
})

const id1 = useRoute().params.id as string

const config = useRuntimeConfig()
const fileViewShow = ref(false)
const fileUrl = ref('')
const view = async (id: string) => {
  const fileList = (await getFileListByObjId({ objId: id })).data
  const file = fileList?.at(0)
  if (file) {
    fileViewShow.value = true
    fileUrl.value = config.public.backendApi + file.filePath
  } else {
    ElMessage.warning('未查询到文件')
  }
}
view(id1)
const router = useRouter();
const route = useRoute();
const id = route.params.id as string


// 返回列表页
const goBack = () => {
  // 使用history.back()保持路由状态
  router.back()
}
</script>