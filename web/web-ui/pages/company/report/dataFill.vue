<template>
  <div class="ml-[12px] relative py-[12px]  pr-[12px]">
    <div class="overflow-hidden rounded-[8px] bg-white px-[10px] pb-[10px] text-left">
      <el-menu class="el-menu-demo" mode="horizontal">
        <el-menu-item index="1">
          数据填报
        </el-menu-item>
      </el-menu>
      <div class="knowledegebg mt-[10px] h-[551px] pl-[70px] pt-[60px]">
        <h1 class="ys_font tit mt-[50px] text-[60px] text-[#318AD9]">
          上海市核电办公数据填报系统
        </h1>
        <p class="mt-[20px] text-[20px]">
          《企事业单位民用核能业务开展情况》填写步骤如下：
        </p>
        <p class="mt-[20px] text-[14px]">
          <span class="rounded-br-lg rounded-tl-lg bg-[#349AF5] px-[15px] py-[6px] color-white"><em
              class="mr-[5px] inline-block h-[12px] w-[3px] bg-white" />第一步</span> 点击【数据填报】，查看历史填报数据，或填报新的年报、半年报数据；
        </p>
        <p class="mt-[20px] text-[14px]">
          <span class="rounded-br-lg rounded-tl-lg bg-[#349AF5] px-[15px] py-[6px] color-white"><em
              class="mr-[5px] inline-block h-[12px] w-[3px] bg-white" />第二步</span>
          点击【报表制度】，查看最新的《上海市民用核能产业统计报表制度》，如对制度已经有所了解，可跳过；
        </p>
        <div class="mx-auto mt-[20px] text-center">
          <el-button type="primary" class="ml-[-90px] mt-[40px] rounded-full"
            @click="toDataFill">
            填报 &nbsp; <span class="iconfont icon-longjiantou text-[16px]" />
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 数据填报
definePageMeta({
  name: 'COMPANY_REPORT_DATAFILL',
  layout: 'company'
})

const toDataFill = () => {
    window.open('https://www.smnpo.cn:18081/datarpt/#/')
}
</script>
<style scoped>
.knowledegebg {
  background: url(/images/data_entrybg.png) no-repeat top center;
  background-size:957px 523px;
}

.tit {
  position: relative;
  z-index: 0;
}

.tit::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 20px;
  width: 350px;
  background: linear-gradient(90deg, #ff870e 0%, rgba(255, 171, 3, 0) 100%);
  height: 12px;
  z-index: -1;
}

.el-button--primary {
  background: linear-gradient(90deg, #ffb157 0%, #ff8800 100%);

  width: 456.92px;
  border: none;
  transition: all 0.3s;
}

.el-button--primary:hover {
  background: linear-gradient(90deg, #ff870e 0%, #ffb157 100%);
}

.el-menu--horizontal {
  --el-menu-horizontal-height: 60px;
}

.el-menu--horizontal>.el-menu-item {
  font-size: 16px;
}
</style>