<template>
  <div class="login pt-[30px]">
    <div class="mx-[auto] w-[450px]">
      <el-tabs v-model="activeName" class="demo-tabs">
        <div class="mt-[1rem] rounded-xl bg-white p-[1rem]">
          <h1 class="ml-[20px] mt-[15px] text-left text-[20px] text-[#333]">
            密码登录
          </h1>
          <p class='text-sm text-[#888] mt-[10px]'>请输入账户名、密码并验证手机号完成登录</p>
          <div class="m-[2rem] mb-[15px] mt-[10px]">

            <div v-if="showFirst">
              <el-form ref="formRef" :model="form" :rules="formRules" label-width="auto" style="max-width: 450px"
                class="rounded-xl bg-white p-[1rem]">
                <!-- <el-form-item prop="userName">
                  <el-input v-model="form.userName" :prefix-icon="User" placeholder="请输入用户名" />
                </el-form-item> -->
                <el-form-item prop="userName">
                  <el-autocomplete v-model="form.userName" :prefix-icon="User" placeholder="请输入完整企业名称"
                    :fetch-suggestions="querySearchAsync as any" @select="handleSelect as any" clearable></el-autocomplete>
                </el-form-item>
                <el-form-item prop="password" class="mt-[2rem]">
                  <el-input v-model="form.password" :prefix-icon="Lock" placeholder="请输入8位以上数字和字母组合密码"
                    type="password" />
                </el-form-item>
                <el-form-item ref="phonenumberRef" prop="phoneNumber" class="mt-[2rem] relative">
                  <el-input v-model="form.phoneNumber" :prefix-icon="Iphone" placeholder="请输入手机号" />
                </el-form-item>
                <el-form-item prop="phoneCode" class="relative mt-[2rem]">
                  <el-input v-model="form.phoneCode" :prefix-icon="Position" placeholder="请输入验证码" />
                  <the-phone-code type="login" :user-name="form.userName" v-slot="data">
                    <el-button size="small" :disabled="data.disabled" @click="sendPhoneCode(data.trigger)"
                      class="absolute right-[2px] top-[2px]">
                      {{ data.disabled ? `重新发送: ${data.countDown}秒` : '发送验证码' }}
                    </el-button>
                  </the-phone-code>
                </el-form-item>
              </el-form>

              <el-button type="primary"
                class="mt-[0.5rem] w-full rounded-[5px] bg-[#318AD9] py-[1.2rem] font-bold transition hover:bg-[#1a7cd2] hover:ease-in"
                @click="handleLogin">
                登录
              </el-button>
            </div>
            <div v-else>
              <el-form ref="formRef" :model="form" :rules="formRules" label-width="auto" style="max-width: 450px"
                class="rounded-xl bg-white p-[1rem]">
                <el-form-item prop="userName">
                  <!-- <el-input v-model="form.userName" :prefix-icon="User" placeholder="请输入完整企业名称" /> -->
                  <el-autocomplete v-model="form.userName" :prefix-icon="User" placeholder="请输入完整企业名称"
                    :fetch-suggestions="querySearchAsync as any" @select="handleSelect as any" clearable></el-autocomplete>
                </el-form-item>
                <el-form-item prop="password" class="mt-[2rem]">
                  <el-input v-model="form.password" :prefix-icon="Lock" placeholder="请输入8位以上数字和字母组合密码"
                    type="password" />
                </el-form-item>
                <el-form-item ref="phonenumberRef" prop="phoneNumber" class="mt-[2rem] relative">
                  <el-input v-model="form.phoneNumber" :prefix-icon="Iphone" placeholder="请输入手机号" />
                </el-form-item>
                <el-form-item prop="phoneCode" class="relative mt-[2rem]">
                  <el-input v-model="form.phoneCode" :prefix-icon="Position" placeholder="请输入验证码" />
                  <the-phone-code type="login" :user-name="form.userName" v-slot="data">
                    <el-button size="small" :disabled="data.disabled" @click="sendPhoneCode(data.trigger)"
                      class="absolute right-[2px] top-[2px]">
                      {{ data.disabled ? `重新发送: ${data.countDown}秒` : '发送验证码' }}
                    </el-button>
                  </the-phone-code>
                </el-form-item>
              </el-form>

              <el-button type="primary"
                class="mt-[0.5rem] w-full rounded-[5px] bg-[#318AD9] py-[1.2rem] font-bold transition hover:bg-[#1a7cd2] hover:ease-in"
                @click="handleLogin">
                登录
              </el-button>
            </div>
            <div class="relative text-center text-sm  mt-2 cursor-pointer text-[#286BBD] hover:text-[#169aff]">
              <a :href="`${config.public.baseUrl}/file/上海市核电办公室门户网站企业中心登录指导手册.docx`" download>登录指引</a>
              <span class="absolute right-0 top-0" @click="showFirst = !showFirst">{{ showFirst ? '非上海企业登陆' : '上海企业登陆' }}</span>
              
            </div>
            <div v-show="showFirst">
              <h2 class="relative mt-[30px] border-t border-[#E7E7E7] text-[14px] text-[#318AD9]">
                <span class="absolute top-[-20px] inline-block translate-x-(-1/2) bg-white p-[10px]">其他登录方式</span>
              </h2>
              <div class="mx-auto mt-[20px] text-center">
                <!-- <img src="/images/dl-weixin.png" @click="$router.push({ name: ECOLUMN_CODE.COMPANY_LOGIN_WX })"> -->
                <el-tooltip content="法人一证通登录" placement="top">
                  <img src="/images/dl-ca.png" @click="loginCa"></img>
                </el-tooltip>
              </div>
            </div>
          </div>
        </div>
      </el-tabs>
    </div>
  </div>
</template>

<script lang="ts" setup>
//登录
definePageMeta({
  name: ECOLUMN_CODE.COMPANY_LOGIN,
  layout: 'company-login'
})
import { ECOLUMN_CODE } from '~/store/column';
import { Iphone, Lock, Position, User } from '@element-plus/icons-vue'
import { reactive, ref } from 'vue'
import type { FormInstance, FormItemInstance, FormRules } from 'element-plus';
import { login, getUserName } from '~/api/user';
import { debounce } from 'lodash';
const config = useRuntimeConfig()
const activeName = ref('first')
const showFirst = ref(true)

const form = reactive({
  userName: '',
  password: '',
  phoneNumber: '',
  phoneCode: ''
})

const formRules = ref<FormRules<typeof form>>({
  userName: [
    { required: true, trigger: 'blur', message: '请输入用户名' },
  ],
  password: [
    { required: true, trigger: 'blur', message: '请输入密码' },
  ],
  phoneNumber: [
    { required: true, trigger: 'blur', message: '请输入手机号' },
  ],
  phoneCode: [
    { required: true, trigger: 'blur', message: '请输入验证码' },
  ]
})

const phonenumberRef = ref<FormItemInstance>()
const sendPhoneCode = (triggerFunc: (phonenumber: string) => any) => {
  phonenumberRef.value?.validate('blur', async valid => {
    if (valid) {
      const res = await triggerFunc(form.phoneNumber)
      if (res.code == 200) {
        ElMessage.success('验证码已发送')
      } else {
        // ElMessage.error('验证码发送失败')
      }
    }
  })
}

const formRef = ref<FormInstance>()
const handleLogin = () => {
  formRef.value?.validate(async (valid) => {
    if (!valid) return

    const res = await login(form.userName, form.password, form.phoneNumber, form.phoneCode)
    if (res.code == 200) {
      ElMessage.success('登录成功')
      useCookie(TOKEN_KEY, { path: config.public.baseUrl }).value = 'Bearer ' + res.token
      const hasShown = localStorage.getItem('welcomeDialogClosed');
      if (hasShown) {
        // 初次访问任意路由时设置标志位
        localStorage.setItem('welcomeDialogClosed', 'null');
      }
      // 登录成功后跳转到首页
      useRouter().push({ name: ECOLUMN_CODE.COMPANY_SERVICE_TRAIN })
    } else {
      ElMessage.error(res.msg)
    }

  })
}

const loginCa = () => {
  const link = document.createElement('a');
  link.href = config.public.caUrl;
  link.target = '_blank';
  link.rel = 'noopener noreferrer';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link); // 清理DOM
}

// 新增用户列表响应式变量
const userList = ref<Array<{ value: any }>>([]);
const loading = ref(false);

// 模糊搜索方法 - 使用防抖优化性能
const querySearchAsync = debounce(async (queryString: string, cb: (arg: any) => void) => {
  if (!queryString) {
    cb([]);
    return;
  }

  loading.value = true;
  try {
    // 调用getUserName接口获取匹配的用户名列表
    const res = await getUserName(queryString);

    // 假设接口返回格式为 { code: 200, data: Array<string> }
    if (res.code === 200 && res.data) {
      // 将返回的数据转换为el-autocomplete需要的格式
      userList.value = res.data.map((item: any) => ({ value: item }));
      cb(userList.value);
    } else {
      cb([]);
    }
  } catch (error) {
    console.error('搜索用户名失败:', error);
    cb([]);
  } finally {
    loading.value = false;
  }
}, 500);

// 用户选择建议项时的处理
const handleSelect = (item: { value: any }) => {
  console.log('选择了用户名:', item.value);
  // 可以在这里添加选择后的额外逻辑
};


</script>

<style scoped>
:deep(.el-input__wrapper) {
  box-shadow: none;
  border-bottom: solid 1px #dcdcdc;
  border-radius: 0;
}

:deep(.el-tabs__nav) {
  justify-content: center !important;
  float: none;
}

:deep(.el-tabs__active-bar) {
  background-color: #fff;
}

:deep(.el-tabs__item.is-active, .el-tabs__item:hover) {
  color: #fff;
  width: 75px;
}

:deep(.el-tabs__item) {
  color: #fff;
}

:deep(.el-tabs__item) {
  padding: 0 50px;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 0;
}
</style>