import { defineNuxtPlugin } from '#app'
import { useRouter } from 'vue-router'
import { ECOLUMN_CODE } from '~/store/column'
import { useUserStore } from '~/store/user'
// import { useUserStore } from '~/store/user'

const whitePages = [
    ECOLUMN_CODE.COMPANY_LOGIN.toString(),
    ECOLUMN_CODE.COMPANY_LOGIN_WX.toString(),
    ECOLUMN_CODE.COMPANY_LOGIN_CA.toString(),
    ECOLUMN_CODE.COMPANY_SERVICE_TRAIN_BEFORE_LOGIN,
    ECOLUMN_CODE.COMPANY_DATA_INDUSTRY_DETAIL,
    'COMPANY_SERVICE_RULE_KNOWLEDGE',
    'COMPANY_SERVICE_RULE_MONTHLY',
    'COMPANY_SERVICE_RULE_WEEKLY',
    'COMPANY_DATA_INDUSTRY_DIRECTORIES',
    'COMPANY_REPORT_MEETING'

]

export default defineNuxtPlugin((nuxtApp) => {
    const router = useRouter()
    router.beforeEach(async (to, from, next) => {
        if (whitePages.includes(to.name?.toString() ?? '') || to.path.split('/')[1] !== 'company') {
            return next()
        }
        if (useCookie(TOKEN_KEY).value) {
            if (!useUserStore().getuserId) {
                await useUserStore().getUserInfo()
            }
        } else {
            if (useCookie(TOKEN_KEY).value) {
                useCookie(TOKEN_KEY).value = null
            }
            
            if (to.name == ECOLUMN_CODE.COMPANY_SERVICE_TRAIN) {
                next({ name: ECOLUMN_CODE.COMPANY_SERVICE_TRAIN_BEFORE_LOGIN })
            } else {
                next({ name: ECOLUMN_CODE.COMPANY_LOGIN })
            }

        }
        next()
    })
})