@import './markdown.css';
@import '../fonts/iconfont.css';
@font-face {
  font-family: 'din_condensedbold';
  src: url(../fonts/din_condensedbold/din_condensed_bold-webfont.svg);
  font-weight: normal;
}
@font-face {
  font-family: 'youshebiaotihei';
  src: url(../fonts/youshebiaotihei/YouSheBiaoTiHei-2.ttf);
  font-weight: normal;
}
html,
body,
#app {
  height: 100%;
  margin: 0;
  padding: 0;
  font-size:16px;
  font-family: Source <PERSON>, Source Han Sans CN;
  color:#333;
}
body {
  background: url(../images/bodybg.png) no-repeat center 195px  #EDF6FF;
  background-size: cover;
  
}
a{color:#333;transition: all 0.4s ease;}
a:hover{color:#479ee4}
html.dark {
  background: #121212;
  color-scheme: dark;
}
img{display:inline-block!important}
#nprogress {
  pointer-events: none;
}

#nprogress .bar {
  background: rgb(13, 148, 136);
  opacity: 0.75;
  position: fixed;
  z-index: 1031;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
}

.li::before{position: absolute; left:2px;top:7px;content: '';width: 5px; height: 5px; background-color: #4792CD;}
.li:hover{color:#479ee4}
.bg_b{ position: relative;display: inline-block; min-height:800px }

.bg_b::after {
  content: '';
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  background: url(../images/b_bg.png) no-repeat center bottom #fff;
  opacity: 0.2; /* 只影响背景图片 */
  z-index: -1; /* 确保它在内容之下 */

}
.bg_b::before {
  content: '';
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  background: #fff;

  z-index: -2; /* 确保它在内容之下 */
}



.neiye{
  @apply mx-auto mt-[5px] web1260 p-[5px] pb-[50px]
}
.banner img{height:145px;max-width: 100%;}
.din_font {
  font-family: 'din_condensedbold';
  font-weight: bold;
}
.ys_font {
  font-family: 'youshebiaotihei';

}
.arial_font {
  font-family: 'arial';
  font-weight: bold;
}

.party_bg_b{ position: relative;display: inline-block; min-height:800px  }

.party_bg_b::after {
  content: '';
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  background: url(../images/party-bg.png) no-repeat center bottom #fff;
  opacity: 0.2; /* 只影响背景图片 */
  z-index: -1; /* 确保它在内容之下 */

}
.party_bg_b::before {
  content: '';
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  background: #fff;

  z-index: -2; /* 确保它在内容之下 */
}

.party_right_bg{
  background: url(../images/party-topbg.png) no-repeat top rgba(255,255,255,0.2);
  background-size:1088px 725px;
  /* background-size:cover; */

}

.partymenu span {
  padding: 0 18px 0 18px;
  margin-right: 10px;
}
.partymenu span:hover {
  background-color: #fff;
  padding: 0 18px 0 18px;
  color: #e02d2d;
  border-radius: 5px 5px 0 0;
  cursor: pointer;
}
.partymenu span.hover {
  background-color: #fff;
  color: #e02d2d;
  border-radius: 5px 5px 0 0;
}
.partymenu a:hover{color:#E02D2D}
.mb-bg {
  height: 45px;
  background: url(../images/party-lmcy.png) no-repeat left top #ffdede;
}
.advancedbg{
  height: 69px;
  background: url(../images/party-dx.png) no-repeat left top;
  width:1025px;
}
.experts{
  background: url(../images/party-expert.png) no-repeat center bottom;
}
.experts::after {
  content: '';
  position: absolute;
 right: 0; bottom: 0;
  background: linear-gradient(90deg, rgba(255, 231, 231, 0.8) 5.07%, rgba(255, 238, 238, 0.8) 100%);
  z-index: -1; /* 确保它在内容之下 */
}
.login{
  background: url(../images/dlbg.png) no-repeat left top;
  height:775px;
}
.bg_enterprise{
  background-color:#EDF3F9;
}
.neiye_enterprise{
  @apply mx-auto web1260  pb-0
}
.bg_menu {
  background: linear-gradient(90deg, #35aeeb 0%, #e2f0fb 100%);
  height: 34px;
  color: #fff;
  text-align: left;
}

.menubg {
  background: linear-gradient(180deg, #54a3e2 0%, #3b97e0 100%);
  border-radius: 5px 5px 0px 0px;
  text-align: left;
}
.menubg a {
  color: #fff;
  padding: 5px 20px;
  margin: 4px 10px 0 10px;
  cursor: pointer;
  font-size: 18px;
  display: inline-block;
}
.menubg a:hover {
  color: #c9e7ff;
}
.menubg a.hover {
  background-color: #fff;
  color: #469ee4;
  display: inline-block;
  border-radius: 5px 3px 0 0;
}
.b_l {
  padding-bottom: 12px;
  border-bottom: 3px solid;
  border-image: linear-gradient(to right, #36b0ff, #0080d4) 1;
  font-weight: 500;
}
.web1260{width:1200px;}

@media (max-width: 1024px) { 
  .web1260{width:1024px;}
  .dsj .shuzi p.two{padding-left:0px !important}
}