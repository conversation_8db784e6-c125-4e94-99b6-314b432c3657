<template>
    <main v-if="!isFullscreen" text="center gray-700 dark:gray-200">
       <layout-the-header></layout-the-header>
        <!-- 页面主体内容 -->
        <div class="pt-2 flex justify-center ">
            <div class="web1260 bg_b p-1 overflow-hidden">
                <LayoutTheBreadNav></LayoutTheBreadNav> 
                <div class="mt-[8px] flex min-h-[700px] ">
                    <LayoutOtherTheSubMenu></LayoutOtherTheSubMenu>
                    <div class="flex-grow overflow-hidden">
                        <nuxt-page></nuxt-page>
                    </div>
                </div>
                
            </div>
        </div>
        <!-- 页面底部 -->
        <layout-the-footer></layout-the-footer>
    </main>
    <main v-else>
        <nuxt-page></nuxt-page>
    </main>
</template>

<script setup lang="ts">

const { isFullscreen } = useFullscreenLayout()
</script>

<style scoped>
.bg_b::after {
  content: '';
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  background: url(/images/b_bg.png) no-repeat center bottom #fff;
  opacity: 0.2; /* 只影响背景图片 */
  z-index: -1; /* 确保它在内容之下 */

}
</style>
