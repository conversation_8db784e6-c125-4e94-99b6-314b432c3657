<template>
    <main text="center gray-700 dark:gray-200">
       <layout-the-header></layout-the-header>
        <!-- 页面主体内容 -->
        <div class="pt-2 flex justify-center ">
            <div class="web1260 party_bg_b p-1">
                <LayoutTheBreadNav></LayoutTheBreadNav>
                <div class="mt-[8px] flex gap-3 min-h-[700px] ">
                    <LayoutPartyTheSubMenu></LayoutPartyTheSubMenu>
                    <div class="party_right_bg flex-grow p-5">
                        <nuxt-page></nuxt-page>
                    </div>
                    
                </div>
                
            </div>
        </div>
        <!-- 页面底部 -->
        <layout-the-footer></layout-the-footer>
    </main>
</template>

<script setup lang="ts">
import { LayoutPartyTheSubMenu } from '#components';



</script>

<style scoped>
.bg_b::after {
  content: '';
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  background: url(/images/b_bg.png) no-repeat center bottom #fff;
  opacity: 0.2; /* 只影响背景图片 */
  z-index: -1; /* 确保它在内容之下 */

}
</style>
