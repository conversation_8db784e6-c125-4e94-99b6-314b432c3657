<template>
    <main text="center gray-700 dark:gray-200">
        <layout-the-header></layout-the-header>
        <!-- 页面主体内容 -->
        <div class="pt-2 flex justify-center">
            <div class="web1260 overflow-hidden">
                <div class="flex bg_enterprise">
                    <div class="w-[198px] bg_left">
                        <LayoutCompanyTheSubMenu></LayoutCompanyTheSubMenu>
                    </div>
                    <!-- <LayoutOtherTheSubMenu></LayoutOtherTheSubMenu> -->
                    <div class="flex-1 min-h-[760px]">
                        <nuxt-page></nuxt-page>
                    </div>
                </div>
            </div>
        </div>
        <!-- 页面底部 -->
        <layout-the-footer></layout-the-footer>
    </main>
</template>

<script setup lang="ts">
import { useUserStore } from '~/store/user';


const userStore = useUserStore()
</script>
<style scoped>
.bg_left {
    background: url(/images/bg_qy.png) no-repeat bottom ;
    background-size: cover;
    margin-bottom: 70px;
}
</style>