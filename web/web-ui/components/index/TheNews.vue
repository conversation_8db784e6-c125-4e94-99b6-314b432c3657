<template>
    <div>
        <div class="flex items-center justify-between pb-2 border_b">
            <span class="text-[#397fb7] text-5 font-bold">新闻中心</span>
        </div>
        <div class="grid grid-cols-[250px_1fr] gap-3 pt-3">
            <img v-if="active == 0" class="w-[250px] h-[197px]"
                :src="`${config.public.backendApi}${partyPhoto}`" alt="">
            <img v-else class="w-[250px] h-[197px]" :src="`${config.public.backendApi}${industryPhoto}`"
                alt="">

            <div class="flex flex-col gap-3">
                <div class="flex justify-between">
                    <div class="flex items-center gap-3">
                        <span :class="active == 0 ? 'text-[#469EE4]' : 'text-[#666]'"
                            class="text-4 font-bold cursor-pointer" @click="setActiveItem(activeRef, 0)">党建动态</span>
                        <span :class="active == 1 ? 'text-[#469EE4]' : 'text-[#666]'"
                            class="text-4 font-bold cursor-pointer" @click="setActiveItem(activeRef, 1)">产业动态</span>
                    </div>
                    <span v-if="active == 0" class="text-[#428BC5] text-3.5 cursor-pointer  transition hover:text-[#449bdf]"
                        @click="$router.push({ name: ECOLUMN_CODE.NEWS_PARTY })">更多>></span>
                    <span v-if="active == 1" class="text-[#428BC5] text-3.5 cursor-pointer  transition hover:text-[#449bdf]"
                        @click="$router.push({ name: ECOLUMN_CODE.NEWS_INDUSTRY })">更多>></span>
                </div>
                <el-carousel ref="activeRef" :initial-index="active" indicator-position="none" arrow="never"
                    height="160px" @change="(cur) => active = cur">
                    <el-carousel-item>
                        <div class="flex flex-col gap-3.5">
                            <div v-for="item in partyList" class="flex items-center justify-between cursor-pointer"
                                @click="$router.push({ name: 'RELEASE', params: { id: item.id, type: 'news' } })">
                                <div class="w-[72%] grid grid-cols-[6%_1fr] items-center">
                                    <div class="w-5px h-5px bg-[#4792CD] ml-1"></div>
                                    <span class="text-[#333333] text-3.5 w-[95%] text-left truncate transition hover:text-[#4792CD]">{{ item.title
                                    }}</span>
                                </div>
                                <span class="text-[#999999] text-3.5 truncate">{{ dateFormat(item.articleDate) }}</span>
                            </div>
                        </div>
                    </el-carousel-item>
                    <el-carousel-item>
                        <div class="flex flex-col gap-3.5">
                            <div v-for="item in industryList" class="flex items-center justify-between cursor-pointer"
                                @click="$router.push({ name: 'RELEASE', params: { id: item.id, type: 'news' } })">
                                <div class="w-[72%] grid grid-cols-[6%_1fr] items-center">
                                    <div class="w-5px h-5px bg-[#4792CD] ml-1"></div>
                                    <span class="text-[#333333] text-3.5 w-[95%] text-left truncate transition hover:text-[#4792CD]">{{ item.title
                                    }}</span>
                                </div>
                                <span class="text-[#999999] text-3.5 truncate">{{ dateFormat(item.articleDate) }}</span>
                            </div>
                        </div>
                    </el-carousel-item>
                </el-carousel>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ECOLUMN_CODE, useColumnStore } from '~/store/column';

const config = useRuntimeConfig()
const active = ref(0)
const activeRef = ref()
const setActiveItem = (ref: any, index: number) => {
    ref.setActiveItem(index)
}

const partyList = ref<IRelease[]>([])
useReleaseList(ECOLUMN_CODE.NEWS_PARTY, 1, 5).then(res => {
    partyList.value = res
})

const industryList = ref<IRelease[]>([])
useReleaseList(ECOLUMN_CODE.NEWS_INDUSTRY, 1, 5).then(res => {
    industryList.value = res
})

const partyPhoto = ref<string>('')
useReleasePhoto(ECOLUMN_CODE.NEWS_PARTY).then(res => {
    partyPhoto.value = res
})

const industryPhoto = ref<string>('')
useReleasePhoto(ECOLUMN_CODE.NEWS_INDUSTRY).then(res => {
    industryPhoto.value = res
})

</script>

<style scoped>
.border_b {
    border-bottom: 2px solid;
    border-image: linear-gradient(90deg, rgba(216, 216, 216, 1), rgba(255, 255, 255, 1)) 2 2;
}
</style>