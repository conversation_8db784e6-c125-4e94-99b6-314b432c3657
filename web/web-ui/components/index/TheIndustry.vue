<template>
    <div>
        <div class="flex items-center justify-between pb-2 border_b">
            <span class="text-[#397fb7] text-5 font-bold">产业发展</span>
        </div>
        <div class="grid grid-cols-[150px_1fr] gap-3 pt-2">
            <div class="flex flex-col gap-3">
                <div :class="{
                    'bg-[#468FC8] w-35': type == 0,
                    'bg-[#F7F8F8] w-30': type != 0
                }" class="text-left rounded-r-3xl py-2 cursor-pointer pl-[1.5rem]" @click="type = 0">
                    <span :class="{ 'text-white': type == 0, 'text-[#606060]': type != 0 }"
                        class=" text-[4.5] transition">世界核电</span>
                </div>
                <div :class="{
                    'bg-[#468FC8] w-35': type == 1,
                    'bg-[#F7F8F8] w-30': type != 1
                }" class="text-left rounded-r-3xl py-2 cursor-pointer pl-[1.5rem]" @click="type = 1">
                    <span :class="{ 'text-white': type == 1, 'text-[#606060]': type != 1 }"
                        class="text-[4.5] transition">中国核电</span>
                </div>
                <div :class="{
                    'bg-[#468FC8] w-35': type == 2,
                    'bg-[#F7F8F8] w-30 ': type != 2
                }" class="text-left rounded-r-3xl py-2 cursor-pointer pl-[1.5rem]" @click="type = 2">
                    <span :class="{ 'text-white': type == 2, 'text-[#606060]': type != 2 }"
                        class="text-[4.5] transition">上海核电</span>
                </div>
            </div>
            <div class="flex flex-col gap-3 pt-3">
                <div class="flex justify-between">
                    <template v-if="type == 0">
                        <headComponent :active-ref="activeRef" :active="active" :list="[
                            { name: '核电机组', code: ECOLUMN_CODE.INDUSTRY_GLOBAL_DATA },
                            { name: '有关政策', code: ECOLUMN_CODE.INDUSTRY_GLOBAL_RULE },
                            { name: '发展动态', code: ECOLUMN_CODE.INDUSTRY_GLOBAL_NEWS }
                        ]"></headComponent>
                    </template>
                    <template v-if="type == 1">
                        <headComponent :active-ref="active1Ref" :active="active1" :list="[
                            { name: '核电机组', code: ECOLUMN_CODE.INDUSTRY_CHINA_DATA },
                            { name: '有关政策', code: ECOLUMN_CODE.INDUSTRY_CHINA_RULE },
                            { name: '发展动态', code: ECOLUMN_CODE.INDUSTRY_CHINA_NEWS }
                        ]"></headComponent>
                    </template>
                    <template v-if="type == 2">
                        <headComponent :active-ref="active2Ref" :active="active2" :list="[
                            { name: '有关政策', code: ECOLUMN_CODE.INDUSTRY_SHANGHAI_RULE },
                            { name: '发展动态', code: ECOLUMN_CODE.INDUSTRY_SHANGHAI_NEWS },
                            { name: '产业地图', code: ECOLUMN_CODE.INDUSTRY_SHANGHAI_MAP },
                            { name: '产业大事记', code: ECOLUMN_CODE.INDUSTRY_SHANGHAI_EVENT },
                        ]"></headComponent>
                    </template>
                </div>
                <el-carousel v-if="type == 0" ref="activeRef" :initial-index="active" indicator-position="none"
                    arrow="never" height="180px" @change="(cur) => active = cur">
                    <el-carousel-item>
                        <div class="flex justify-between">
                            <!-- <img class="h-180px w-[310px]" src="/images/map.png"> -->
                            <table class="ml-[10px] w-[100%] border-collapse border border-[#F1F1F1]">
                                <thead>
                                    <tr>
                                        <th colspan="2" class="bg-[#6FBDE7] py-[6px] text-white">
                                            世界核电
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="text-[15px]">
                                    <tr>
                                        <td class="border border-[#F1F1F1] bg-[#DCF3FB] py-[6px] color-[#367595]">
                                            在建机组
                                        </td>
                                        <td class="border border-[#F1F1F1] py-[6px]">
                                            {{ worldJzInfo?.zjUnitCounts }}台
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="border border-[#F1F1F1] bg-[#DCF3FB] py-[6px] color-[#367595]">
                                            装机容量
                                        </td>
                                        <td class="border border-[#F1F1F1] py-[6px]">
                                            {{ worldJzInfo?.zjInstalledCapacity }}MWe
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="border border-[#F1F1F1] bg-[#DCF3FB] py-[6px] color-[#367595]">
                                            在运机组
                                        </td>
                                        <td class="border border-[#F1F1F1] py-[6px]">
                                            {{ worldJzInfo?.zyUnitCounts }}台
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="border border-[#F1F1F1] bg-[#DCF3FB] py-[6px] color-[#367595]">
                                            装机容量
                                        </td>
                                        <td class="border border-[#F1F1F1] py-[6px]">
                                            {{ worldJzInfo?.zyInstalledCapacity }}Mwe
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </el-carousel-item>
                    <el-carousel-item>
                        <listComponent type="industry-world" :list="globalRuleList"></listComponent>
                    </el-carousel-item>
                    <el-carousel-item>
                        <listComponent type="industry-world" :list="globalNewsList"></listComponent>
                    </el-carousel-item>
                </el-carousel>
                <el-carousel v-if="type == 1" ref="active1Ref" :initial-index="active1" indicator-position="none"
                    arrow="never" height="180px" @change="(cur) => active1 = cur">
                    <el-carousel-item>
                        <div class="flex justify-between">
                            <!-- <img class="h-180px" src="/images/map1.png"> -->
                            <!-- <NuxtImg class="h-180px" src="/images/map1.png"></NuxtImg> -->
                            <table class="ml-[10px] w-[100%] border-collapse border border-[#F1F1F1]">
                                <thead>
                                    <tr>
                                        <th colspan="2" class="bg-[#6FBDE7] py-[6px] text-white">
                                            中国核电
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="text-[15px]">
                                    <tr>
                                        <td class="border border-[#F1F1F1] bg-[#DCF3FB] py-[6px] color-[#367595]">
                                            在建机组
                                        </td>
                                        <td class="border border-[#F1F1F1] py-[6px]">
                                            {{ chinaJzInfo?.zjUnitCounts }}台
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="border border-[#F1F1F1] bg-[#DCF3FB] py-[6px] color-[#367595]">
                                            装机容量
                                        </td>
                                        <td class="border border-[#F1F1F1] py-[6px]">
                                            {{ chinaJzInfo?.zjInstalledCapacity }}MWe
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="border border-[#F1F1F1] bg-[#DCF3FB] py-[6px] color-[#367595]">
                                            在运机组
                                        </td>
                                        <td class="border border-[#F1F1F1] py-[6px]">
                                            {{ chinaJzInfo?.zyUnitCounts }}台
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="border border-[#F1F1F1] bg-[#DCF3FB] py-[6px] color-[#367595]">
                                            装机容量
                                        </td>
                                        <td class="border border-[#F1F1F1] py-[6px]">
                                            {{ chinaJzInfo?.zyInstalledCapacity }}Mwe
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </el-carousel-item>
                    <el-carousel-item>
                        <listComponent type="industry-china" :list="chinaRuleList"></listComponent>
                    </el-carousel-item>
                    <el-carousel-item>
                        <listComponent type="industry-china" :list="chinaNewsList"></listComponent>
                    </el-carousel-item>
                </el-carousel>
                <el-carousel v-if="type == 2" ref="active2Ref" :initial-index="active1" indicator-position="none"
                    arrow="never" height="180px" @change="(cur) => active2 = cur">
                    <el-carousel-item>
                        <listComponent type="industry-shanghai" :list="shanghaiRuleList"></listComponent>
                    </el-carousel-item>
                    <el-carousel-item>
                        <listComponent type="industry-shanghai" :list="shanghaiNewsList"></listComponent>
                    </el-carousel-item>
                    <el-carousel-item>
                        <div class="flex justify-between">

                            <div class="w-[160px] mapbg mr-[20px] flex justify-center">
                                <div class="map relative">
                                   
                                </div>
                            </div>

                            <div class="rightbg flex-1 ">

                                <div class="exchangecon custom-box">
                                    <div class="menubghover" @click="$router.push({name: ECOLUMN_CODE.INDUSTRY_SHANGHAI_MAP_INDUSTRY})"><div class="menubg">企业分布图</div></div>
                                     <div class="menubg" @click="$router.push({name: ECOLUMN_CODE.INDUSTRY_SHANGHAI_MAP_INDUSTRY})">创新资源分布图</div>
                                      <div class="menubg" @click="$router.push({name: ECOLUMN_CODE.INDUSTRY_SHANGHAI_MAP_INDUSTRY})">产业基地分布图</div>
                                </div>
                            </div>

                        </div>
                    </el-carousel-item>
                    <el-carousel-item>
                        <!-- <listComponent :list="shanghaiEventList"></listComponent> -->
                        <div v-for="item in shanghaiEventList"
                            class="flex items-center justify-between cursor-pointer mb-[10px]"
                            @click="toEventPage(item)">
                            <div class="w-[80%] grid grid-cols-[4%_1fr] items-center">
                                <div class="w-5px h-5px bg-[#4792CD] ml-1"></div>
                                <span class="text-[#666] text-3.5 w-[95%] text-left truncate">{{ item.name }}</span>
                            </div>
                            <span class="text-[#999999] text-3.5 truncate">{{ dateFormat(item.occurTime) }}</span>
                        </div>
                    </el-carousel-item>
                </el-carousel>
            </div>
        </div>
    </div>
</template>

<script setup lang="tsx">
import { getChinaJZInfo } from '~/api/crawler/china'
import { getWorldJZInfo } from '~/api/crawler/global'
import { getContentMemorabiliaBasicList, getContentMemorabiliaInfoList } from '~/api/release'
import { ECOLUMN_CODE, useColumnStore } from '~/store/column'

const router = useRouter()

const type = ref(0)
const active = ref(0)
const active1 = ref(0)
const active2 = ref(0)

const activeRef = ref()
const active1Ref = ref()
const active2Ref = ref()
const setActiveItem = (ref: any, index: number) => {
    ref.setActiveItem(index)
}

const globalRuleList = ref<IRelease[]>([])
useReleaseList(ECOLUMN_CODE.INDUSTRY_GLOBAL_RULE, 1, 5).then(res => {
    globalRuleList.value = res
})
const globalNewsList = ref<IRelease[]>([])
useReleaseList(ECOLUMN_CODE.INDUSTRY_GLOBAL_NEWS, 1, 5).then(res => {
    globalNewsList.value = res
})

const chinaRuleList = ref<IRelease[]>([])
useReleaseList(ECOLUMN_CODE.INDUSTRY_CHINA_RULE, 1, 5).then(res => {
    chinaRuleList.value = res
})
const chinaNewsList = ref<IRelease[]>([])
useReleaseList(ECOLUMN_CODE.INDUSTRY_CHINA_NEWS, 1, 5).then(res => {
    chinaNewsList.value = res
})

const shanghaiRuleList = ref<IRelease[]>([])
useReleaseList(ECOLUMN_CODE.INDUSTRY_SHANGHAI_RULE, 1, 5).then(res => {
    shanghaiRuleList.value = res
})
const shanghaiNewsList = ref<IRelease[]>([])
useReleaseList(ECOLUMN_CODE.INDUSTRY_SHANGHAI_NEWS, 1, 5).then(res => {
    shanghaiNewsList.value = res
})
const shanghaiEventList = ref<any[]>([])

const columnId = getColumnIdByCode(ECOLUMN_CODE.INDUSTRY_SHANGHAI_EVENT)
getContentMemorabiliaBasicList({columnId: columnId}, {pageNum: 1, pageSize: 5}).then(res => {
    shanghaiEventList.value = res.rows ?? []
})

const headComponent = defineComponent({
    props: {
        list: {
            type: Array<{ name: string, code: string }>,
            default: () => []
        },
        activeRef: {
            type: Object,
            default: () => null
        },
        active: {
            type: Number,
            default: 1
        }
    },
    render() {
        return (
            <>
                <div class="flex items-center gap-3">
                    {this.list.map((item, index) => {
                        return (
                            <span
                                class={`text-4 font-bold cursor-pointer transition  ${this.active === index ? 'text-[#469EE4]' : 'text-[#666]'
                                    }`}
                                onClick={() => setActiveItem(this.activeRef, index)}
                            >
                                {item.name}
                            </span>
                        )
                    })}
                </div>
                {this.list.map((item, index) => {
                    return (this.active === index && (
                        <span class="text-[#428BC5] text-3.5 cursor-pointer  transition hover:text-[#449bdf]"
                            onClick={() => router.push({ name: item.code })}>更多{'>>'}</span>
                    ))

                })}
            </>
        )
    }
})

const listComponent = defineComponent({
    props: {
        type: {
            type: String,
            default: ''
        },
        list: {
            type: Array<IRelease>,
            default: () => []
        },
    },
    render() {
        return (
            this.list.map(item => {
                return (
                    <div class="flex items-center justify-between cursor-pointer mb-[10px]"
                        onClick={() => router.push({ name: 'RELEASE', params: { id: item.id, type: this.type } })}>
                        <div class="w-[80%] grid grid-cols-[4%_1fr] items-center">
                            <div class="w-5px h-5px bg-[#4792CD] ml-1"></div>
                            <span
                                class="text-[#666] text-3.5 w-[95%] text-left truncate transition hover:text-[#4792CD]">{item.title}</span>
                        </div>
                        <span class="text-[#999999] text-3.5 truncate">{dateFormat(item.articleDate)}</span>
                    </div>
                )
            })

        )
    }
})

const worldJzInfo = ref<any>()
getWorldJZInfo().then(res => {
    worldJzInfo.value = res.data
})

const chinaJzInfo = ref<any>()
getChinaJZInfo().then(res => {
    chinaJzInfo.value = res.data
})

const toEventPage = (row: any) => {
    useRouter().push({ name: ECOLUMN_CODE.INDUSTRY_SHANGHAI_EVENT, query: { year: dateFormat(row.occurTime, 'YYYY') } })
}
</script>

<style scoped>
.border_b {
    border-bottom: 2px solid;
    border-image: linear-gradient(90deg, rgba(216, 216, 216, 1), rgba(255, 255, 255, 1)) 2 2;
}


.menubg {
    @apply text-[#0391EF] relative pl-[3.5rem] cursor-pointer text-[16px] leading-[40px];
    background: url(/images/bluebg.png) no-repeat;
    background-size: contain;
    width: 350px;
    height: 40px;
    margin-top: 15px;
}

.menubg::before {
    @apply absolute w-[36px] h-[36px] left-[20px] top-[0.2rem];
    content: '';
    background: url(/images/bluepoint.png) no-repeat;
}

.menubghover .menubg {
    @apply text-[#FC8B00] cursor-pointer;
    background: url(/images/yellowbg.png) no-repeat;
     background-size: contain;
    width: 350px;
    height: 40px;
  
}

.menubghover .menubg::before {
    content: '';
    background: url(/images/yellowpoint.png) no-repeat;
}

.box {
    transform: scale(0.585);

    transform-origin: 0 0;

}

.topbg_industry {
    background: url(/images/zyfw_topbg.png) no-repeat top;
    background-size: cover;
    height: 55px;
    width: 100%;
}

.mapbg {
    background: url(/images/zyfw_bg1.png) no-repeat center 0px #D9F2FF;
}

.map {
    background: url(/images/industry_map1.gif) no-repeat center;
    background-size: contain;
    /* width: 100%;
    height: 100%; */
    width: 180px;
    height: 190px;
}

.allpoint {
    @apply left-0 top-0;
    background: url(/images/industrymap_all.png) no-repeat center;
    background-size: contain;
    width: 100%;
    height: 100%;

}

.allpoint_pink {
    @apply left-0 top-0;
    background: url(/images/industrymap_all_pink.png) no-repeat center;
    background-size: contain;
    width: 100%;
    height: 100%;
}

.allpoint_yellow {
    @apply left-0 top-0;
    background: url(/images/industrymap_all_yellow.png) no-repeat center;
    background-size: contain;
    width: 100%;
    height: 100%;
}

.left {
    width: 28.5rem;
}

.h-20 {
    height: 20rem;
}

.flex-container {
    display: flex;

}

.rightbg {
    @apply text-left bg-white/25 backdrop-blur-sm;
    /* border: 1.5px solid #ffffff; */
    border-radius: 10px;
    overflow-y: auto;
    overflow-x:hidden;

}

.topmenu {
    @apply border-b border-[#97C1D7] relative flex text-[14px] pl-[15px] pb-[0.1rem] text-[#5A5A5A];
}

.topmenu::before {
    @apply absolute w-[15px] h-[5px] left-0 bottom-0 bg-[#0391EF];
    content: '';
}

.topmenu span {
    @apply mx-[10px];
}

.topmenu span:hover {
    @apply text-[#0391EF] cursor-pointer;
}

.topmenu span.hover {
    @apply text-[#0391EF] cursor-pointer;
}

.exchangecon {
    @apply text-sm leading-[20px] leading-[35px];
}

.gradient-text {
    @apply text-base mr-[5px];
    background: linear-gradient(45deg, #55bbff, #0099ff);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: bold;
}

.exchangecon .tit {
    @apply mt-[10px] mb-[15px] text-base;
}

.exchangecon .all {
    @apply text-[#0391EF] relative pl-[2.4rem] cursor-pointer;
}

.exchangecon .all::before {
    @apply absolute w-[36px] h-[36px] left-0;
    content: '';
    background: url(/images/bluepoint.png) no-repeat;
}

.exchangecon .all:hover {
    @apply text-[#027fd1];
}

.exchangecon .pink {
    @apply text-[#FD525B] relative pl-[2.4rem] cursor-pointer;
}

.exchangecon .pink:hover {
    @apply text-[#f63b45];
}

.exchangecon .pink::before {
    @apply absolute w-[36px] h-[36px] left-0;
    content: '';
    background: url(/images/pinkpoint.png) no-repeat;
}

.exchangecon .yellow {
    @apply text-[#FE8616] relative pl-[2.4rem] cursor-pointer;
}

.exchangecon .yellow::before {
    @apply absolute w-[36px] h-[36px] left-0;
    content: '';
    background: url(/images/yellowpoint.png) no-repeat;
}

.exchangecon .yellow:hover {
    @apply text-[#f27d0f];
}
</style>