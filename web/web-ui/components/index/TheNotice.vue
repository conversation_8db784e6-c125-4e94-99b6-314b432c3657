<template>
    <div class="mt-[5px] h-[40px] flex justify-between bg-[#FAFAFA] px-[10px] leading-[40px]">
        <div class="flex items-center flex-1 w-[75%]">
            <i class="iconfont icon-tongzhi color-[#3A81B9]" />
            <span class="ml-[5px] text-[#FD7216] font-bold">通知公告</span>

            <div class="flex items-center  gap-1 ml-3 flex-1">
                <div class="w-5px h-5px bg-[#4792CD]"></div>
                <div class="flex-1 text-left">
                    <el-carousel  height="40px" direction="vertical" :interval="5000">
                        <el-carousel-item v-for="item in noticeList" :key="item.id" class="truncate">
                            <span class="text-[#333333] text-3.5 cursor-pointer transition hover:text-[#4792CD]" @click="$router.push({ name: 'RELEASE', params: { id: item.id, type: 'notice' } })">{{ item?.title }}</span>
                        </el-carousel-item>
                    </el-carousel>
                </div>
            </div>
        </div>
        <span class="text-[#428BC5] text-3.5 cursor-pointer  transition hover:text-[#449bdf]"
            @click="$router.push({ name: ECOLUMN_CODE.NOTICE_NOTICE })">
            更多>>
        </span>
    </div>
</template>
<script lang="ts" setup>
import { ECOLUMN_CODE, useColumnStore } from '~/store/column'

const columnStore = useColumnStore()

const notice = ref<IRelease>()
const noticeList  = ref<IRelease[]>([])
useReleaseList(ECOLUMN_CODE.NOTICE_NOTICE, 1, 5).then(res => {
   noticeList.value = res??[]
})
</script>
