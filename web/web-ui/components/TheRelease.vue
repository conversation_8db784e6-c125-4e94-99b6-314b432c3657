<template>
    <div id="printSection" class="ml-[0px] overflow-hidden pr-[0px] break-all mb-[100px]">
        <div class="px-[5px] py-[5px]">
            <div class="relative bg-white px-[50px] pb-[50px] pt-[30px] text-zinc-600 ">
                <div class="text-center text-[24px] font-bold">
                    {{ detail?.title }}
                </div>
                <div class="mt-[20px] flex justify-between border-b pb-[16px] text-center text-sm no-print">
                    <div class="flex text-zinc-400">
                        <div  v-if="detail?.source" class="flex items-center">
                            <span class="iconfont icon-caidan" />稿件来源：{{ detail?.source }}
                        </div>
                        <div class="flex items-center">
                            <span class="iconfont icon-shijian ml-[15px]" />发布日期：{{ dateFormat(detail?.articleDate,
                            'YYYY年MM月DD日') }}
                        </div>
                        <div class="flex items-center">
                            <span class="iconfont icon-liulanliang ml-[15px]" />浏览量：<i
                                class="text-[#fb0e24] not-italic">{{ (detail?.readCount ?? random(1,107, false))+200 }}</i>
                        </div>

                    </div>

                    <div class="flex">
                        <!-- <div class="mr-[20px] text-right text-sm">
                            <a href="#" class="text-[#666]"><span class="iconfont icon-fangda text-[#469EE4]" /> 放大
                            </a><a href="#" class="ml-[5px] text-[#666]"><span
                                    class="iconfont icon-suoxiao text-[#469EE4]" /> 缩小</a>
                        </div> -->
                        <!-- <span class="text-zinc-400">分享至：</span>
                        <NuxtImg src="/images/weixin.png" width="18px" class="flex-0 mr-[5px] h-[18px]"></NuxtImg>
                        <NuxtImg src="/images/weibo.png" width="18px" class="flex-0 mr-[5px] h-[18px]"></NuxtImg>
                        <NuxtImg src="/images/QQ.png" width="18px" class="flex-0 mr-[5px] h-[18px]"></NuxtImg>
                        <NuxtImg src="/images/douban.png" width="18px" class="flex-0 mr-[5px] h-[18px]"></NuxtImg> -->
                        
                    </div>
                </div>
                <TheZoom :theme="theme">
                    <div v-html="detail?.content"></div>
                </TheZoom>

                <ThePrintTool target-area-id="printSection" :theme="theme"></ThePrintTool>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { random } from 'lodash';
import { getArticleById } from '~/api/release';

const props = defineProps<{
    id: string
    theme?: string
}>()

const detail = ref<IRelease>()
getArticleById(props.id).then(res => {
    detail.value = res.data
})

</script>

<style scoped>
@media print {
    .no-print {
        display: none;
    }
}
</style>