<template>
    <div class="search-page items-center justify-center">
        <div class="search_box">
            <!-- <div class="go-left"></div> -->
            <div class="search">
                <el-input v-model="title" :maxlength="100" style="height: 36px;">
                    <template #append>
                        <el-button @click="search">搜索</el-button>
                    </template>
                </el-input>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
const emit = defineEmits(['close'])
const title  = ref('')
const search = () => { 
	emit('close')
	useRouter().push({name: 'SEARCH_RESLUT', query: {title: title.value}})
}
</script>
<style scoped lang="scss">
:deep(.el-input-group__append button.el-button) {
    color: #fff;
    font-size: 15px;
    height: 36px;
    width: 100px;
    background: #0080d4;
    border-radius: 0 5px 5px 0;
}

:deep(.el-input-group__append button.el-button):hover {
    color: #fff;
    font-size: 15px;
    height: 36px;
    width: 100px;
    background: #16a0fa;
    border-radius: 0 5px 5px 0;
}

:deep(.el-input__wrapper.is-focus) {
    box-shadow: none;
}

:deep(.el-input__wrapper) {
    box-shadow: none;
	border:solid 1px #e2e2e2;
}


/*search-page*/
.search-page {
    
	height: 100%;
	display: flex;
	background:#f7f9fd;
	// left: 0;
	// position: absolute;
	//  top: 40px;
	// z-index: 8;
	// padding-bottom: 20px;
}

.search_box {
	width: 900px;
	margin: auto;
}

.search-page .search {
	clear: both;
	width: 60%;
	margin: auto;
	border-radius: 0 5px 5px 0;
}

.search-page .search input.input_text {
	width: 80%;
	float: left;
	z-index: 9;
}

.search-page .search input.input_submit {
	width: 20%;
}

.search select {
	border: none;
	height: 36px;
	right: 20%;
	outline: none;
	z-index: 9;
	position: absolute;
	background: #fff;
}

.search-page .go-left {
	background: url(../images/prev_close.png) no-repeat left center;
	width: 32px;
	height: 32px;
	float: right;
	background-size: 32px;
	margin: 20px;
}

.hot-search {
	margin: 40px 20px;
	overflow: hidden;
	text-align: center;
}

.hot-search p {
	font-size: 18px;
	margin-bottom: 10px;
	font-weight: bold;
	color: #1d273d;
}

.search-paihang li {
	display: inline-block;
	line-height: 26px;
	height: 26px;
	font-size: 15px;
	margin: 3px 10px;
}

.search-paihang li a {
	display: block;
	color: #666;
}

.search-paihang li a:hover {
	color: #1d273d
}

</style>