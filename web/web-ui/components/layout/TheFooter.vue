<template>
    <div class="bg-[rgba(66,139,197,1)] w-full fixed bottom-0 z-10 custom-popover-container h-[55px]">
        <div class="mx-auto web1260 flex flex-col py-[5px] pb-[12px] text-left text-[#f3f3f3] relative">
            <div class="flex items-center justify-between mt-[5px]">
                <el-popover placement="right" trigger="hover" :show-arrow="false"  width="1120px"  popper-class="transparent-popover" class="mt-[22px]">
                    
                    <template #reference>
                       <span class="text-white w-[86px] mt-[5px] text-sm"><i class="iconfont icon-youqinglianjie text-white/80"/> 友情链接</span>
                    </template>
                    <template #default>
                        <div class="ml-[16px] text-base ]">
                    <el-select :teleported="false" placeholder="国家部委网站" size="large" style="width: 160px" @change="handleChange" class="mr-[30px] show-space-select" popper-class="show-space-option">
                        <el-option v-for="item in links" :key="item.dictCode" :label="item.dictLabel"
                            :value="item.dictValue ?? ''" />
                    </el-select>
                    <el-select :teleported="false" placeholder="本市政府部门网站" size="large" style="width: 170px" @change="handleChange" class="mr-[30px] show-space-select" popper-class="show-space-option">
                        <el-option v-for="item in links1" :key="item.dictCode" :label="item.dictLabel"
                            :value="item.dictValue ?? ''" />
                    </el-select>
                    <el-select :teleported="false"  placeholder="协会学会网站" size="large" style="width: 160px" @change="handleChange" class="mr-[30px] show-space-select" popper-class="show-space-option">
                        <el-option v-for="item in links2" :key="item.dictCode" :label="item.dictLabel"
                            :value="item.dictValue ?? ''" />
                    </el-select>
                    
                    <el-select :teleported="false"  placeholder="有关央企" size="large" style="width: 150px" @change="handleChange" class="mr-[30px] show-space-select" popper-class="show-space-option">
                        <el-option v-for="item in links3" :key="item.dictCode" :label="item.dictLabel"
                            :value="item.dictValue ?? ''" />
                    </el-select>
                    <el-select :teleported="false"  placeholder="在沪单位" size="large" style="width: 180px" @change="handleChange" class="show-space-select" popper-class="show-space-option">
                        <el-option v-for="item in links4" :key="item.dictCode" :label="item.dictLabel"
                            :value="item.dictValue ?? ''" />
                    </el-select>
                </div>
                    </template>
                </el-popover>

                 <div class="text-xs leading-[22px] mt-[5px] mr-[50px]">
                    <div class="flex justify-end">
                <p class="text-right">主办单位：上海市核电办公室&nbsp; <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">沪ICP备2022026005号-1</a>
                   &nbsp;&nbsp;  邮编：200032 &nbsp;&nbsp; 电话：021-64173211 &nbsp;&nbsp;传真：021-64173212  &nbsp;&nbsp; 地址：医学院路69号二楼&nbsp;&nbsp; 推荐使用360浏览器及1920*1080分辨率
                </p>

               </div>
               
                <a href="https://bszs.conac.cn/sitename?method=show&id=27AEA0149E8D6166E053012819ACE50D" target="_blank"
                    class="absolute right-0 top-[3px]"><img src="/public/images/sylogo.png" class="w-[45px]"></a>
            </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getDictByCode } from '~/api/dict';

const links = ref<SysDictData[]>([])
getDictByCode('friendship_link1').then(res => {
    links.value = res.data ?? []
})
const links1 = ref<SysDictData[]>([])
getDictByCode('friendship_link2').then(res => {
    links1.value = res.data ?? []
})
const links2 = ref<SysDictData[]>([])
getDictByCode('friendship_link3').then(res => {
    links2.value = res.data ?? []
})
const links3 = ref<SysDictData[]>([])
getDictByCode('friendship_link4').then(res => {
    links3.value = res.data ?? []
})
const links4 = ref<SysDictData[]>([])
getDictByCode('friendship_link5').then(res => {
    links4.value = res.data ?? []
})

const handleChange = (val: string) => {
    if (val && val.includes('http')) {
        window.open(val)
    }
}
</script>

<style scoped>
:deep(.el-select__wrapper) {
    color: #fff;
    box-shadow: none;
    background: none;
}

:deep(.el-select__placeholder) {
    color: #fff;
}

:deep(.el-select__caret) {
    color: #fff;
}
/* 下拉选项保留空格 */
.show-space-option .el-select-dropdown__item {
  white-space: pre;   /* 保留空格和换行，允许自动换行 */
    /*white-space: nowrap;        禁止换行 */
  padding: 0 20px;  
}
/* 下拉框容器样式 */
.el-select-dropdown {
  min-width: auto !important; /* 清除默认最小宽度 */
  width: auto !important;     /* 允许宽度自适应 */
  max-width: 800px;           /* 避免过长（按需调整） */
}


/* 输入框内保留空格（防合并） */
.show-space-select .el-input__inner {
  white-space: nowrap;     /* 单行显示，保留连续空格 */
}

</style>

<style>



/* 组合基础类名 + 自定义类名提升优先级 */

.el-popover.transparent-popover{
  background:rgba(66,139,197,1) !important; /* 黑色70%透明 */
  backdrop-filter: blur(13px);  /*毛玻璃效果 */
  border: none !important;
  color: #fff;
}
/* 箭头适配所有方向 */
.transparent-popover .popper__arrow,
.transparent-popover .popper__arrow::after {
  border-color:  rgba(0, 0, 0, 0.3) !important; /* 先隐藏默认箭头 */
}
.popper__arrow::before{background-color: transparent!important;}
.el-popper[data-popper-placement^=right]>.el-popper__arrow{
    border-left-color: rgba(0, 0, 0, 0.3) !important;
    /* background-color:rgba(0, 0, 0, 0.8) !important; */
}
.el-popover.el-popper{padding:5px; box-shadow: none;}
.el-popper.is-light>.el-popper arrow:before {
background:transparent !important;border:1px solid  rgba(0, 0, 0, 0.8) !important;}

/* .transparent-popover.el-popper__arrow, .transparent-popover.el-popper__arrow:before{background-color:rgba(0, 0, 0, 0.8) !important ;} */
/* 底部箭头 */
.transparent-popover[x-placement^="bottom"] .popper__arrow::after {
  border-bottom-color: rgba(0, 0, 0, 0.8) !important; /* 与背景一致 */
}
.el-popper.is-light arrow:before{background: transparent!important;}
/* 顶部箭头 */
.transparent-popover[x-placement^="top"] .popper__arrow::after {
  border-top-color: rgba(0, 0, 0, 0.8) !important;
}

/* 左侧箭头 */
.transparent-popover[x-placement^="left"] .popper__arrow::after {
  border-left-color: rgba(0, 0, 0, 0.8) !important;
}

/* 右侧箭头 */
.transparent-popover[x-placement^="right"] .popper__arrow::after {
  border-right-color: rgba(0, 0, 0, 0.8) !important;
}

</style>