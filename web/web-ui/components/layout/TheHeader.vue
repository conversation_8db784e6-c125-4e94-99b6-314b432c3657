<template>
    <!-- banner -->
    <div class="bg-[#E9F3FD] h-[36px] relative z-100">
        <div class="mx-auto web1260">
            <div class=" flex justify-between  text-sm color-[#3A81B9] font-400 leading-[36px] ">
                <!-- 时间 -->
                <div>
                    <TheSystemTime></TheSystemTime>
                </div>
                <!-- 常用操作 -->
                <div class="flex justify-end">
                    <a style="margin-right: 10px;" href="https://www.smnpo.cn/">切换旧版网站 ></a>
                    <div class="search mr-[10px]">
                        <el-input v-model="title" :maxlength="100" style="height: 26px;">
                            <template #append>
                                <el-button @click="search"><em class="iconfont icon-sousuo text-white text-bold" /></el-button>
                            </template>
                        </el-input>
                    </div>
                    <!-- <span
                        class="mr-[10px] mt-[5px] h-[20px] w-[20px] cursor-pointer rounded-full bg-[#3A81B9] color-white leading-[20px]"
                        @click="handleSearch"><em class="iconfont icon-sousuo" /></span> -->
                    <div class="flex hover:text-[#428BC5]">
                        <span class="iconfont icon-yonghu" />
                        <span v-if="!userStore.isLogin" class="cursor-pointer"
                            @click="$router.push({ name: ECOLUMN_CODE.COMPANY_LOGIN })">登录</span>
                        <span v-else class="cursor-pointer" @click="logout">退出登录</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- <el-drawer v-model="searchPopUp" direction="ttb" :show-close="false" size="150">
        <TheSearch @close="searchPopUp = false"></TheSearch>
    </el-drawer> -->
    <div class="topbg h-[135px]">
        <div class="mx-auto web1260 flex justify-between">
            <div class="mt-[35px]">
                <img src="/public/images/logo.png" class="h-[70px]">
            </div>
            <div class="mt-[25px]">
                <img src="/public/images/top_text.svg" class="h-[80px] mt-[7px]">
            </div>
        </div>
    </div>
    <!-- 主菜单 -->
    <div class="h-[40px] w-full bg-[#428BC5] leading-[40px]">
        <div class="mx-auto web1260 flex justify-between text-[18px] text-white font-500">
            <span v-for="(item, index) in columnStore.getColumnTree" :key="index"
                :class="{ 'module-active': isActive(item.columnPcUrl) }"
                class="w-[180px] cursor-pointer hover:bg-[#256FAA]" @click="toPage(item)">{{
                    item.columnName }}</span>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ECOLUMN_CODE, useColumnStore } from '~/store/column'
import { useUserStore } from '~/store/user'

const config = useRuntimeConfig()
const userStore = useUserStore()
const columnStore = useColumnStore()
const route = useRoute()
const router = useRouter()
const emit = defineEmits(['close'])
const title = ref('')
const search = () => {
    emit('close')
    useRouter().push({ name: 'SEARCH_RESLUT', query: { title: title.value } })
}
function isActive(path: string) {
    if (path === '/' && route.path === '/')
        return true
    if (path === route.path)
        return true

    // 比较两者的第一个单词
    return (path.split('/')[1] === route.path.split('/')[1])
}

const toPage = (item: IColumn) => {
    if (item.columnCode == 'COMPANY') {
        router.push({ name: ECOLUMN_CODE.COMPANY_SERVICE_TRAIN })
    } else {
        router.push({ path: item.columnPcUrl })
    }


}

const logout = () => {
    userStore.logout()
    useCookie(TOKEN_KEY, { path: config.public.baseUrl }).value = null
    useRouter().push({ name: ECOLUMN_CODE.COMPANY_LOGIN })
}

const searchPopUp = ref(false)
const handleSearch = () => {
    searchPopUp.value = !searchPopUp.value
}
// watch([searchPopUp], (searchPopUpVal) => {
//     document.body.style.overflow = searchPopUpVal  ? 'hidden' : '';
// }, { immediate: true });
</script>

<style scoped lang="less">
.topbg {
    background: url(/images/topbg.png) no-repeat top;
    background-size: cover;
}

.module-active {
    background-color: #256faa;
}

:deep(.el-drawer__body) {
    padding: 0px;
    // overflow: hidden;
}
</style>

<style lang="less">
.el-drawer__body {
    padding: 0px;
    // overflow: hidden;
}

.el-drawer__header {
    display: none;
}

.search {
margin-top:-2px;
}

.search input.input_text {
    width: 60%;
    float: left;
    z-index: 9;
}

.search input.input_submit {
    width: 10%;
}

.search select {
    border: none;
    height: 36px;
    right: 20%;
    outline: none;
    z-index: 9;
    position: absolute;
    background: #fff;

}
.search .el-button{padding:5px 2px}
.search .el-input-group__append{background-color: #007fb4;border:none;padding:0 15px}
.search .el-input__wrapper{box-shadow: none;padding: 0 10px}
.search .el-input-group__append{box-shadow: none;}
.search .el-input__inner{width: 120px;height:20px}
.el-popup-parent--hidden {
    width: 100% !important;
}

// .el-drawer.ttb {
//     width: 100% !important;
//     /* 强制宽度为 100% */
//     left: 0 !important;
//     /* 避免偏移 */
// }</style>